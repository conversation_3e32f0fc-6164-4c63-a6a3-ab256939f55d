import json
from pathlib import Path
from loguru import logger


class TextToInlineConverter:
    """
    Конвертирует текстовые файлы в inline-формат, подходящий для вставки в JSON.
    Заменяет переносы строк на '\n' и экранирует кавычки.
    """

    def __init__(self, escape_unicode: bool = False):
        self.converted_count = 0
        self.escape_unicode = escape_unicode

    def _convert_text_to_inline(self, text: str) -> str:
        """
        Преобразует текст в inline-формат для JSON.
        Заменяет переносы строк на '\n' и экранирует кавычки.

        Если self.escape_unicode=False (по умолчанию), сохраняет Unicode-символы в оригинальном виде.
        Если self.escape_unicode=True, преобразует Unicode-символы в escape-последовательности (\\uXXXX).
        """
        # Используем json.dumps для правильного экранирования
        # Это автоматически обрабатывает кавычки, переносы строк и другие спецсимволы
        if self.escape_unicode:
            # Стандартное поведение json.dumps - преобразует Unicode в \uXXXX
            return json.dumps(text)[1:-1]  # Убираем начальные и конечные кавычки
        else:
            # Сохраняем Unicode-символы в оригинальном виде
            return json.dumps(text, ensure_ascii=False)[
                1:-1
            ]  # Убираем начальные и конечные кавычки

    def convert_file(self, file_path: Path) -> Path | None:
        """
        Конвертирует один текстовый файл в inline-формат.
        Сохраняет inline-файл рядом с оригинальным файлом с суффиксом '-inline'.
        """
        if not file_path.is_file():
            logger.error(f"Файл не найден: {file_path}")
            return None

        try:
            # Чтение исходного файла
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Преобразование в inline-формат
            inline_content = self._convert_text_to_inline(content)

            # Создание нового имени файла с суффиксом '-inline'
            output_path = file_path.with_stem(f"{file_path.stem}-inline")

            # Запись результата
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(inline_content)

            logger.info(f"Файл успешно конвертирован: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"Ошибка при конвертации файла {file_path}: {e}")
            return None

    def convert_directory(
        self, directory_path: Path, file_extensions: list[str] = None
    ) -> int:
        """
        Конвертирует все текстовые файлы с указанными расширениями в указанной директории.
        По умолчанию обрабатывает файлы .txt и .md
        """
        if not directory_path.is_dir():
            logger.error(f"Директория не найдена: {directory_path}")
            return 0

        if file_extensions is None:
            file_extensions = [".txt", ".md"]

        converted_count = 0
        for ext in file_extensions:
            for file_path in directory_path.glob(f"*{ext}"):
                # Пропускаем файлы, которые уже имеют суффикс '-inline'
                if file_path.stem.endswith("-inline"):
                    continue

                if self.convert_file(file_path):
                    converted_count += 1

        logger.info(
            f"Завершена конвертация. Конвертировано {converted_count} файлов в inline-формат."
        )
        return converted_count
