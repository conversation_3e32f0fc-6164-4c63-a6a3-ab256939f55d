import json
from pathlib import Path
from loguru import logger


class JsonToReadableConverter:
    """
    A utility class to convert unreadable JSON files to readable format.
    Based on the algorithm from decode_json.py.
    """

    def __init__(self):
        logger.info("JsonToReadableConverter initialized.")

    def convert_file(self, json_file_path: Path) -> Path | None:
        """
        Converts a single JSON file to a readable format.

        Args:
            json_file_path: The path to the input JSON file.

        Returns:
            The path to the generated readable JSON file, or None if conversion fails.
        """
        if not json_file_path.exists():
            logger.warning(f"JSON file not found: {json_file_path}")
            return None
        if not json_file_path.is_file():
            logger.warning(f"Path is not a file: {json_file_path}")
            return None

        try:
            # Step 1: Read the original JSON file
            with open(json_file_path, "r", encoding="utf-8") as f_in:
                # Step 2: Load JSON data (automatically converts \uXXXX to normal characters)
                data = json.load(f_in)

            # Step 3: Create output filename with -readable suffix
            readable_file_path = json_file_path.with_stem(
                f"{json_file_path.stem}-readable"
            )

            # Step 4: Save data to new file with readable formatting
            with open(readable_file_path, "w", encoding="utf-8") as f_out:
                # ensure_ascii=False - main parameter to keep Cyrillic as is, not convert to \uXXXX
                # indent=4 - makes JSON file beautiful and readable with indentation
                json.dump(data, f_out, ensure_ascii=False, indent=4)

            logger.info(f"Converted {json_file_path} to {readable_file_path}")
            return readable_file_path

        except FileNotFoundError:
            logger.error(f"File not found: {json_file_path}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format in {json_file_path}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error converting {json_file_path}: {e}")
            return None

    def convert_directory(self, directory_path: Path) -> int:
        """
        Converts all JSON files in a given directory to readable format.

        Args:
            directory_path: The path to the directory containing JSON files.

        Returns:
            The number of JSON files successfully converted.
        """
        if not directory_path.exists() or not directory_path.is_dir():
            logger.error(f"Directory not found or is not a directory: {directory_path}")
            return 0

        converted_count = 0
        for json_file in directory_path.glob("*.json"):
            if self.convert_file(json_file):
                converted_count += 1

        logger.info(
            f"Finished converting JSON files in directory {directory_path}. Total converted: {converted_count}"
        )
        return converted_count
