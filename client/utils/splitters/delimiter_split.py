"""Разделитель файлов на основе разделителей, который разбивает файлы по определенным шаблонам разделителей.

Использует иерархический подход поиска разделителей:
1. Сначала ищет разделители "---"
2. Затем заголовки Markdown (H1 -> H2 -> H3 -> H4)
3. В последнюю очередь переносы строк (двойные, затем одиночные)
"""

import logging
import re
from typing import NamedTuple

from .base import BaseSplitter, SplitConfig

logger = logging.getLogger(__name__)


class DelimiterMatch(NamedTuple):
    """Представляет найденный разделитель."""

    position: int
    priority: int
    pattern: str
    match_text: str


class DelimiterSplitter(BaseSplitter):
    """Разделитель, использующий иерархический поиск разделителей для оптимального разделения."""

    # Иерархические группы разделителей в порядке приоритета
    DELIMITER_GROUPS = {
        "separators": {
            "priority": 0,
            "patterns": [r"\n\s*---\s*\n"],  # Высокий приоритет: "---"
        },
        "headings": {
            "priority": 1,
            "patterns": [
                r"\n#{1}\s+",  # H1 - наивысший приоритет среди заголовков
                r"\n#{2}\s+",  # H2
                r"\n#{3}\s+",  # H3
                r"\n#{4}\s+",  # H4 - самый низкий приоритет среди заголовков
            ],
        },
        "newlines": {
            "priority": 2,
            "patterns": [
                r"\n\n+",  # Множественные переносы строк
                r"\n",  # Одиночные переносы строк
            ],
        },
    }

    def __init__(
        self,
        config: SplitConfig,
        delimiter_patterns: list[str] | None = None,
    ):
        super().__init__(config)
        # Кэш для найденных разделителей
        self._delimiter_cache = {}

        # Если переданы кастомные паттерны, используем их для группы separators
        if delimiter_patterns:
            self.DELIMITER_GROUPS["separators"]["patterns"] = delimiter_patterns

    def _find_delimiters_by_group(
        self, content: str, group_name: str
    ) -> list[DelimiterMatch]:
        """Находит все разделители для конкретной группы."""
        group = self.DELIMITER_GROUPS[group_name]
        delimiters = []

        for pattern_idx, pattern in enumerate(group["patterns"]):
            # Приоритет внутри группы: меньший индекс = выше приоритет
            priority = group["priority"] * 100 + pattern_idx

            for match in re.finditer(pattern, content):
                delimiters.append(
                    DelimiterMatch(
                        position=match.start(),
                        priority=priority,
                        pattern=pattern,
                        match_text=match.group(0),
                    )
                )

        return delimiters

    def find_delimiter_positions(self, content: str) -> list[int]:
        """Находит позиции разделителей, используя иерархический подход.

        Возвращает отсортированный список позиций, включая начало и конец файла.
        """
        # Кэшируем результат для одного и того же контента
        content_hash = hash(content)
        if content_hash in self._delimiter_cache:
            return self._delimiter_cache[content_hash]

        all_delimiters = []

        # Собираем разделители из всех групп
        for group_name in self.DELIMITER_GROUPS:
            group_delimiters = self._find_delimiters_by_group(content, group_name)
            all_delimiters.extend(group_delimiters)

        # Сортируем по позиции, затем по приоритету
        all_delimiters.sort(key=lambda d: (d.position, d.priority))

        # Убираем дубликаты позиций, оставляя разделитель с наивысшим приоритетом
        unique_positions = []
        last_position = -1

        for delimiter in all_delimiters:
            if delimiter.position != last_position:
                unique_positions.append(delimiter.position)
                last_position = delimiter.position

        # Добавляем начало и конец файла
        positions = [0] + unique_positions + [len(content)]
        positions = sorted(set(positions))  # Убираем дубликаты и сортируем

        # Кэшируем результат
        self._delimiter_cache[content_hash] = positions

        logger.debug(f"Найдено {len(positions) - 2} уникальных позиций разделителей")
        return positions

    def find_split_positions(self, content: str) -> list[int]:
        """Определяет оптимальные точки разделения, используя иерархический подход."""
        total_length = len(content)

        # Определяем язык и получаем соответствующие константы размера
        language = self.detect_language(content)
        max_file_size, target_part_size, max_part_size, min_part_size = (
            self.config.get_adjusted_sizes(language)
        )

        logger.info(f"Обработка текста на {language} с иерархическим разделением")
        logger.debug(
            f"Размеры - MAX_FILE: {max_file_size}, TARGET: {target_part_size}, "
            f"MAX_PART: {max_part_size}, MIN_PART: {min_part_size}"
        )

        # Если файл достаточно мал, возвращаем его как есть
        if total_length <= max_file_size:
            return [0, total_length]

        # Пробуем найти разделители иерархически
        split_positions = self._find_hierarchical_splits(
            content, target_part_size, max_part_size, min_part_size
        )

        if len(split_positions) <= 2:
            logger.warning("Не удалось найти подходящие точки разделения")
            return [0, total_length]

        return split_positions

    def _find_hierarchical_splits(
        self, content: str, target_size: int, max_size: int, min_size: int
    ) -> list[int]:
        """Ищет точки разделения иерархически по группам разделителей."""
        total_length = len(content)

        # Пробуем каждую группу разделителей по порядку приоритета
        for group_name in ["separators", "headings", "newlines"]:
            logger.debug(f"Пробуем разделение по группе: {group_name}")

            delimiters = self._find_delimiters_by_group(content, group_name)
            if not delimiters:
                continue

            # Сортируем по позиции
            delimiters.sort(key=lambda d: d.position)
            positions = [d.position for d in delimiters]

            # Пробуем найти оптимальные точки разделения
            split_positions = self._find_optimal_splits_from_positions(
                positions, total_length, target_size, max_size, min_size
            )

            if len(split_positions) > 2:  # Больше чем просто начало и конец
                logger.info(
                    f"Успешное разделение по группе '{group_name}' на {len(split_positions) - 1} частей"
                )
                return split_positions

        logger.warning("Не удалось найти подходящие разделители ни в одной группе")
        return [0, total_length]

    def _find_optimal_splits_from_positions(
        self,
        delimiter_positions: list[int],
        total_length: int,
        target_size: int,
        max_size: int,
        min_size: int,
    ) -> list[int]:
        """Находит оптимальные точки разделения из списка позиций разделителей."""
        if not delimiter_positions:
            return [0, total_length]

        # Добавляем начало и конец, если их нет
        all_positions = sorted(set([0] + delimiter_positions + [total_length]))

        # Используем жадный алгоритм для поиска оптимальных разделений
        split_points = [0]
        current_pos = 0

        while current_pos < total_length:
            best_next_pos = None
            best_score = float("-inf")

            # Ищем лучшую следующую позицию
            for pos in all_positions:
                if pos <= current_pos:
                    continue

                part_size = pos - current_pos

                # Проверяем ограничения размера
                if part_size < min_size and pos != total_length:
                    continue
                if part_size > max_size:
                    break  # Позиции отсортированы, дальше будет только хуже

                # Вычисляем оценку для этой позиции
                score = self._calculate_split_score(
                    part_size, target_size, pos, total_length
                )

                if score > best_score:
                    best_score = score
                    best_next_pos = pos

            # Если не нашли подходящую позицию, останавливаемся
            if best_next_pos is None:
                break

            # Если это не конец файла, добавляем точку разделения
            if best_next_pos != total_length:
                split_points.append(best_next_pos)

            current_pos = best_next_pos

        # Всегда добавляем конец файла
        if split_points[-1] != total_length:
            split_points.append(total_length)

        return split_points

    def _calculate_split_score(
        self, part_size: int, target_size: int, position: int, total_length: int
    ) -> float:
        """Вычисляет оценку для точки разделения."""
        # Базовая оценка: насколько близко к целевому размеру
        size_score = 1.0 - abs(part_size - target_size) / target_size

        # Бонус за то, что не слишком близко к концу файла (избегаем маленьких последних частей)
        remaining = total_length - position
        if remaining > 0:
            end_penalty = max(0, 1.0 - remaining / target_size) * 0.3
        else:
            end_penalty = 0

        return size_score - end_penalty

    def _post_process_part(
        self, content: str, part_index: int, total_parts: int
    ) -> str:
        """Post-process part content. Can be overridden by subclasses.

        Removes delimiters and trims whitespace/newlines from the start and end.
        """
        if not content:
            return content

        # Удаление разделителей только в начале и конце
        for pattern in self.DELIMITER_GROUPS["separators"]["patterns"]:
            # Удаляем в начале
            content = re.sub(f"^{pattern}", "", content)
            # Удаляем в конце
            content = re.sub(f"{pattern}$", "", content)

        # Удаление "---" только в начале и конце
        content = re.sub(r"^\s*---\s*", "", content, flags=re.IGNORECASE)
        content = re.sub(r"\s*---\s*$", "", content, flags=re.IGNORECASE)

        # Обрезка пробелов и переносов строк в начале и в конце
        content = content.strip()

        return content

    def split_file(self, file_path: str) -> bool:
        """Разбивает файл на части, используя иерархический подход поиска разделителей."""
        try:
            # Очищаем кэш для нового файла
            self._delimiter_cache.clear()

            # Читаем содержимое файла
            with open(file_path, encoding="utf-8") as file:
                content = file.read()

            file_size = len(content)

            # Определяем язык и получаем соответствующие константы размера
            language = self.detect_language(content)
            max_file_size, target_part_size, max_part_size, min_part_size = (
                self.config.get_adjusted_sizes(language)
            )

            # Проверяем, нужно ли разделение
            if file_size <= max_file_size:
                logger.info(
                    f"Файл {file_path} меньше {max_file_size} символов ({language}), пропускаем"
                )
                return False

            logger.info(
                f"Обработка файла: {file_path} ({file_size} символов, {language})"
            )

            # Ищем точки разделения
            split_points = self.find_split_positions(content)

            # Валидируем точки разделения
            if not self.validate_split_points(split_points, file_size, max_part_size):
                logger.warning(
                    f"Не удалось найти подходящие точки разделения для {file_path}"
                )
                return False

            # Дополнительная валидация: проверяем, что точки находятся на разделителях
            delimiter_positions = self.find_delimiter_positions(content)
            for i in range(1, len(split_points) - 1):  # Пропускаем начало и конец
                if split_points[i] not in delimiter_positions:
                    logger.warning(
                        f"Точка разделения в позиции {split_points[i]} не является разделителем"
                    )
                    return False

            # Разделяем файл и сохраняем части
            success = self._save_parts(file_path, content, split_points)

            if success:
                logger.info(
                    f"Файл {file_path} успешно разделен на {len(split_points) - 1} частей"
                )

            return success

        except Exception as e:
            logger.error(f"Ошибка при разделении файла {file_path}: {e}")
            return False

    def validate_split_points(
        self, split_points: list[int], content_length: int, max_part_size: int
    ) -> bool:
        """Валидирует точки разделения для создания приемлемых частей."""
        # Базовая валидация от родительского класса
        if not super().validate_split_points(
            split_points, content_length, max_part_size
        ):
            return False

        # Дополнительная валидация для разделителей
        if len(split_points) < 2:
            logger.warning("Недостаточно точек разделения")
            return False

        # Проверяем, что первая и последняя точки корректны
        if split_points[0] != 0 or split_points[-1] != content_length:
            logger.warning("Некорректные начальная или конечная точки разделения")
            return False

        # Проверяем, что точки отсортированы и уникальны
        for i in range(1, len(split_points)):
            if split_points[i] <= split_points[i - 1]:
                logger.warning(
                    f"Точки разделения не отсортированы или содержат дубликаты: {split_points[i - 1]} >= {split_points[i]}"
                )
                return False

        logger.debug(f"Валидация пройдена для {len(split_points) - 1} частей")
        return True
