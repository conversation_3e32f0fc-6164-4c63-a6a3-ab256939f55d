import sys

import questionary
from questionary import Style  # Import Style again
from loguru import logger

from client.utils.operations.file_splitting_operation import FileSplittingOperation
from client.utils.operations.ttml_to_txt_operation import TtmlToTxtOperation
from client.utils.operations.srt_to_txt_operation import SrtToTxtOperation
from client.utils.operations.json_to_readable_operation import JsonToReadableOperation
from client.utils.operations.duplicate_finder_operation import DuplicateFinderOperation
from client.utils.operations.file_merging_operation import FileMergingOperation
from client.utils.operations.fb2_epub_splitting_operation import (
    Fb2EpubSplittingOperation,
)
from client.utils.operations.text_to_inline_operation import TextToInlineOperation


# Configure Loguru to write to file and disable console output
logger.remove()  # Remove default handler (console output)
logger.add(
    "prepare.log",
    rotation="5 MB",  # Rotate file every 5 MB
    compression="zip",  # Compress old logs
    level="INFO",
    format=(
        "<green>{time:HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    ),
)


def main():
    """Main function to prompt for arguments and process files."""
    # Define a custom style for blue questions within main
    custom_style = Style(
        [
            ("qmark", "fg:#00BFFF bold"),
            ("question", "fg:#00BFFF bold"),
            ("answer", "fg:#FF9D00 bold"),
            ("pointer", "fg:#FF9D00 bold"),
            ("highlighted", "fg:#FF9D00 bold"),
            ("separator", "fg:#cc5454"),
            ("instruction", ""),
            ("text", ""),
            ("disabled", "fg:#858585 italic"),
        ]
    )

    questionary.confirm(
        "Добро пожаловать в интерактивную систему обработки файлов. Начать?",
        default=True,
        style=custom_style,
    ).ask()

    logger.info("### Выбор операции ###")
    operation_mode = questionary.select(
        "Выберите операцию:",
        choices=[
            "Конвертация TTML в TXT",
            "Конвертация SRT в TXT",
            "Конвертация JSON в читаемый формат",
            "Конвертация текста в inline-формат для JSON",
            "Разделение больших файлов",
            "Разделение FB2/EPUB по главам",
            "Объединение текстовых файлов",
            "Поиск дубликатов",
        ],
        default="Разделение больших файлов",
        style=custom_style,
    ).ask()

    if operation_mode == "Разделение больших файлов":
        operation = FileSplittingOperation(custom_style)
    elif operation_mode == "Конвертация TTML в TXT":
        operation = TtmlToTxtOperation(custom_style)
    elif operation_mode == "Конвертация SRT в TXT":
        operation = SrtToTxtOperation(custom_style)
    elif operation_mode == "Конвертация JSON в читаемый формат":
        operation = JsonToReadableOperation(custom_style)
    elif operation_mode == "Поиск дубликатов":
        operation = DuplicateFinderOperation(custom_style)
    elif operation_mode == "Объединение текстовых файлов":
        operation = FileMergingOperation(custom_style)
    elif operation_mode == "Разделение FB2/EPUB по главам":
        operation = Fb2EpubSplittingOperation(custom_style)
    elif operation_mode == "Конвертация текста в inline-формат для JSON":
        operation = TextToInlineOperation(custom_style)
    else:
        logger.error("Неизвестный режим операции.")
        sys.exit(1)

    operation.run()

    logger.info("Операция завершена.")


if __name__ == "__main__":
    main()
