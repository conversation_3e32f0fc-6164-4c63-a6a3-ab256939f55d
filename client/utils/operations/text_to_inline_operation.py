from pathlib import Path

import questionary
from questionary import Style
from loguru import logger

from client.utils.operations.base_operation import BaseOperation
from client.utils.converters.text_to_inline_converter import TextToInlineConverter


class TextToInlineOperation(BaseOperation):
    def __init__(self, custom_style: Style):
        super().__init__(custom_style)

    def run(self) -> None:
        logger.info("### Конвертация текстовых файлов в inline-формат для JSON ###")

        # Получаем директорию с файлами
        directory = self.get_directory_input(
            "Введите директорию с текстовыми файлами (текущая директория: .):"
        )
        directory_path = Path(directory)

        # Запрашиваем расширения файлов
        extensions_input = questionary.text(
            "Введите расширения файлов через запятую (по умолчанию: txt,md):",
            default="txt,md",
            style=self.custom_style,
        ).ask()

        # Запрашиваем режим преобразования Unicode-символов
        escape_unicode = questionary.confirm(
            "Преобразовывать Unicode-символы в escape-последовательности (\\uXXXX)? (по умолчанию: нет):",
            default=False,
            style=self.custom_style,
        ).ask()

        # Обрабатываем введенные расширения
        extensions = [f".{ext.strip()}" for ext in extensions_input.split(",")]
        logger.info(f"Будут обработаны файлы с расширениями: {extensions}")

        # Подтверждение операции
        self.confirm_execution("Начать конвертацию текстовых файлов в inline-формат?")

        # Выполняем конвертацию
        converter = TextToInlineConverter(escape_unicode=escape_unicode)
        converted_count = converter.convert_directory(directory_path, extensions)

        # Логируем информацию о режиме преобразования Unicode
        unicode_mode = (
            "с преобразованием Unicode"
            if escape_unicode
            else "с сохранением оригинальных Unicode-символов"
        )
        logger.info(f"Режим конвертации: {unicode_mode}")

        logger.info(
            f"Конвертация в inline-формат завершена. Обработано файлов: {converted_count}"
        )
