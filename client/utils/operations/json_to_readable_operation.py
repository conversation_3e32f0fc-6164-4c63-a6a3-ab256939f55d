from client.utils.operations.base_operation import BaseOperation
from client.utils.converters.json_to_readable_converter import JsonToReadableConverter
from loguru import logger
from pathlib import Path
from questionary import Style


class JsonToReadableOperation(BaseOperation):
    def __init__(self, custom_style: Style):
        super().__init__(custom_style)

    def run(self) -> None:
        logger.info("### Конвертация JSON в читаемый формат ###")
        directory = self.get_directory_input(
            "Введите директорию с JSON файлами (текущая директория: .):"
        )

        self.confirm_execution("Начать конвертацию JSON в читаемый формат?")

        logger.info(f"Starting JSON to readable conversion in directory: {directory}")
        converter = JsonToReadableConverter()
        converted_count = converter.convert_directory(Path(directory))
        logger.info(f"Total JSON files converted: {converted_count}")
