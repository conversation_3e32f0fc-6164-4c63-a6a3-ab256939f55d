# base_classes.py

from abc import ABC, abstractmethod
from enum import Enum
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


class TaskStatus(Enum):
    """Статусы задач обработки файлов."""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"
    SKIPPED = "skipped"


@dataclass
class TaskResult:
    """Результат выполнения задачи."""

    status: TaskStatus
    message: Optional[str] = None
    error: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class BaseFileTask(ABC):
    """Абстрактный базовый класс для задач обработки файлов."""

    def __init__(self, file_path: Path, task_params: Dict[str, Any]):
        self.file_path = file_path
        self.task_params = task_params
        self.status = TaskStatus.PENDING
        self.result: Optional[TaskResult] = None
        self.skip_reason: Optional[str] = None

    @abstractmethod
    async def process(self) -> TaskResult:
        """Обрабатывает файл и возвращает результат."""
        pass

    def should_skip(self) -> bool:
        """Определяет, нужно ли пропустить обработку файла."""
        skip_result = self._check_skip_conditions()
        if skip_result:
            self.skip_reason = skip_result
            return True
        return False

    @abstractmethod
    def _check_skip_conditions(self) -> Optional[str]:
        """
        Проверяет условия пропуска файла.
        Возвращает строку с причиной пропуска или None, если не нужно пропускать.
        """
        pass

    def get_display_name(self) -> str:
        """Возвращает отображаемое имя файла."""
        return self.file_path.name

    def get_skip_reason(self) -> str:
        """Возвращает причину пропуска файла."""
        return self.skip_reason or "Неизвестная причина"

    def update_status(self, status: TaskStatus, result: Optional[TaskResult] = None):
        """Обновляет статус задачи."""
        self.status = status
        if result:
            self.result = result


class BaseTaskOrchestrator(ABC):
    """Абстрактный базовый класс-оркестратор для управления задачами."""

    def __init__(self, directory: Path, task_params: Dict[str, Any]):
        self.directory = directory
        self.task_params = task_params
        self.tasks: List[BaseFileTask] = []
        self.stats = {"success": 0, "errors": 0, "skipped": 0, "details": []}

    @abstractmethod
    def get_task_name(self) -> str:
        """Возвращает название типа задачи."""
        pass

    @abstractmethod
    def get_task_description(self) -> str:
        """Возвращает описание задачи."""
        pass

    @abstractmethod
    def create_file_task(self, file_path: Path) -> BaseFileTask:
        """Создает задачу для конкретного файла."""
        pass

    @abstractmethod
    def get_file_pattern(self) -> str:
        """Возвращает паттерн для поиска файлов (например, '**/*.txt')."""
        pass

    def discover_files(self) -> List[Path]:
        """Находит файлы для обработки в директории."""
        pattern = self.get_file_pattern()
        return sorted(list(self.directory.glob(pattern)))

    def create_tasks(self) -> List[BaseFileTask]:
        """Создает список задач для всех найденных файлов."""
        files = self.discover_files()
        self.tasks = [self.create_file_task(file_path) for file_path in files]
        return self.tasks

    async def process_all_tasks(self, progress_callback=None) -> Dict[str, Any]:
        """Обрабатывает все задачи и возвращает статистику."""
        if not self.tasks:
            self.create_tasks()

        for i, task in enumerate(self.tasks):
            # Уведомляем о начале обработки задачи
            if progress_callback:
                progress_callback(task, i, len(self.tasks))

            try:
                if task.should_skip():
                    task.update_status(TaskStatus.SKIPPED)
                    self.stats["skipped"] += 1
                    # Уведомляем об обновлении статуса
                    if progress_callback:
                        progress_callback(task, i, len(self.tasks))
                else:
                    task.update_status(TaskStatus.PROCESSING)
                    # Уведомляем об обновлении статуса
                    if progress_callback:
                        progress_callback(task, i, len(self.tasks))

                    result = await task.process()
                    task.update_status(result.status, result)

                    if result.status == TaskStatus.COMPLETED:
                        self.stats["success"] += 1
                    elif result.status == TaskStatus.ERROR:
                        self.stats["errors"] += 1
                        if result.error:
                            self.stats["details"].append(
                                (task.get_display_name(), result.error)
                            )
                    elif result.status == TaskStatus.SKIPPED:
                        self.stats["skipped"] += 1

                    # Уведомляем об обновлении статуса
                    if progress_callback:
                        progress_callback(task, i, len(self.tasks))

            except Exception as e:
                error_msg = f"Неожиданная ошибка: {str(e)}"
                task.update_status(
                    TaskStatus.ERROR, TaskResult(TaskStatus.ERROR, error=error_msg)
                )
                self.stats["errors"] += 1
                self.stats["details"].append((task.get_display_name(), error_msg))
                # Уведомляем об обновлении статуса
                if progress_callback:
                    progress_callback(task, i, len(self.tasks))

        return self.stats

    def get_summary_data(self) -> Dict[str, Any]:
        """Возвращает данные для отчета."""
        return {
            "task_name": self.get_task_name(),
            "directory": self.directory,
            "total_files": len(self.tasks),
            "stats": self.stats,
            "task_params": self.task_params,
        }
