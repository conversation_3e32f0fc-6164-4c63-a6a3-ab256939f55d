# universal_interface.py

from datetime import timedelta
from typing import Optional
from collections import deque

from rich.panel import Panel
from rich.progress import (
    Progress,
    BarColumn,
    TextColumn,
    SpinnerColumn,
    TaskProgressColumn,
    TimeElapsedColumn,
    TaskID,
)
from rich.table import Table
from rich.console import Console, Group

from client.processing_modules.base_classes import (
    BaseFileTask,
    BaseTaskOrchestrator,
    TaskStatus,
)
from client.processing_modules.config import (
    BAR_WIDTH,
    MAX_VISIBLE_FILE_TASKS,
)


def trim_filename(filename: str, max_length: int = 50, ellipsis: str = "...") -> str:
    """Обрезает имя файла до указанной длины, добавляя многоточие при необходимости.
    Гарантирует, что возвращаемая строка всегда имеет длину max_length,
    дополняя пробелами справа, если она короче.
    """
    if len(filename) <= max_length:
        result = filename
    else:
        # Оставляем расширение файла
        name, ext = filename.rsplit(".", 1) if "." in filename else (filename, "")
        ext = f".{ext}" if ext else ""

        # Вычисляем доступную длину для имени с учетом расширения и многоточия
        available_length = max_length - len(ext) - len(ellipsis)
        if available_length <= 0:
            # На крайний случай, если max_length слишком мал, просто обрезаем
            result = filename[:max_length]
        else:
            # Обрезаем имя и добавляем многоточие и расширение
            result = f"{name[:available_length]}{ellipsis}{ext}"

    if len(result) < max_length:
        result = result.ljust(max_length)

    # Добиваем пробелами до нужной длины
    return result


def format_time(seconds: float) -> str:
    """Форматирует секунды в строку HH:MM:SS."""
    return str(timedelta(seconds=int(seconds)))


class UniversalInterface:
    """Универсальный интерфейс для отображения процесса обработки файлов."""

    def __init__(self, console: Console):
        self.console = console
        self.progress: Optional[Progress] = None
        self._visible_file_tasks: deque[TaskID] = deque(maxlen=MAX_VISIBLE_FILE_TASKS)

    def display_task_header(self, orchestrator: BaseTaskOrchestrator):
        """Отображает заголовок с названием типа обработки."""
        task_name = orchestrator.get_task_name()
        self.console.print(f"\n[bold cyan]🔄 {task_name}[/bold cyan]")

    def display_processing_plan(self, orchestrator: BaseTaskOrchestrator):
        """Отображает план обработки."""
        files = orchestrator.discover_files()
        task_description = orchestrator.get_task_description()

        self.console.print("\n[bold]План обработки:[/bold]")
        self.console.print(
            f" • [bold]Папка:[/bold] [cyan]{orchestrator.directory.resolve()}[/cyan]"
        )
        self.console.print(
            f" • [bold]Тип обработки:[/bold] [cyan]{task_description}[/cyan]"
        )
        self.console.print(
            f" • [bold]Обнаружено файлов:[/bold] [cyan]{len(files)}[/cyan]"
        )

        # Отображаем дополнительные параметры задачи, если есть
        if orchestrator.task_params:
            excluded_keys = ["directory", "file_processor", "summarization_strategy"]
            for key, value in orchestrator.task_params.items():
                if key not in excluded_keys and not str(value).startswith("<"):
                    self.console.print(
                        f" • [bold]{key.replace('_', ' ').title()}:[/bold] [cyan]{value}[/cyan]"
                    )

    def create_progress_bar(self) -> Progress:
        """Создает и возвращает экземпляр Progress."""
        self.progress = Progress(
            SpinnerColumn(),
            "|",
            TextColumn("[progress.description]{task.description}"),
            BarColumn(bar_width=BAR_WIDTH),
            TaskProgressColumn(),
            "|",
            TimeElapsedColumn(),
            "|",
            TextColumn("{task.fields[status]}"),
            console=self.console,
        )
        return self.progress

    def add_file_task(self, description: str) -> TaskID:
        """
        Добавляет новую задачу файла в прогресс-бар, скрывая самую старую,
        если превышен лимит. Создает эффект "прокрутки".
        """
        if not self.progress:
            raise RuntimeError("Progress bar has not been initialized.")

        # Если очередь уже заполнена, самая старая задача будет автоматически удалена
        # благодаря maxlen. Нам нужно только скрыть её в progress.
        if len(self._visible_file_tasks) == MAX_VISIBLE_FILE_TASKS:
            oldest_task_id = self._visible_file_tasks[
                0
            ]  # peek at the one that will be removed
            self.progress.update(oldest_task_id, visible=False)

        new_task_id = self.progress.add_task(description, total=None, status="⏳")
        self._visible_file_tasks.append(new_task_id)
        return new_task_id

    def update_file_task(
        self, task_id: TaskID, task: BaseFileTask, description: str = None
    ):
        """Обновляет задачу файла в зависимости от статуса."""
        if not self.progress:
            return

        status_icons = {
            TaskStatus.PENDING: "⏳",
            TaskStatus.PROCESSING: "🔄",
            TaskStatus.COMPLETED: "✅",
            TaskStatus.ERROR: "❌",
            TaskStatus.SKIPPED: "⏩",
        }

        status_colors = {
            TaskStatus.PENDING: "yellow",
            TaskStatus.PROCESSING: "white",
            TaskStatus.COMPLETED: "green",
            TaskStatus.ERROR: "red",
            TaskStatus.SKIPPED: "cyan",
        }

        status_texts = {
            TaskStatus.PENDING: "В очереди",
            TaskStatus.PROCESSING: "Обработка",
            TaskStatus.COMPLETED: "Готово",
            TaskStatus.ERROR: "Ошибка",
            TaskStatus.SKIPPED: "Пропущено",
        }

        if description is None:
            # Получаем отформатированное имя файла фиксированной длины (20 символов)
            display_name = task.get_display_name()
            formatted_name = trim_filename(display_name, max_length=70)

            color = status_colors.get(task.status, "white")
            status_text = status_texts.get(task.status, task.status.value.title())

            # Добавляем причину пропуска для пропущенных файлов
            if (
                task.status == TaskStatus.SKIPPED
                and hasattr(task, "skip_reason")
                and task.skip_reason
            ):
                # description = f"[{color}]{status_text} ({task.skip_reason}): {formatted_name}[/{color}]"
                description = f"[{color}]{status_text}: {formatted_name}[/{color}]"
            elif task.status == TaskStatus.PROCESSING:
                description = (
                    f"[bold white]{status_text}: {formatted_name}[/bold white]"
                )
            else:
                description = f"[{color}]{status_text}: {formatted_name}[/{color}]"

        icon = status_icons.get(task.status, "❔")

        update_params = {"description": description, "status": icon}

        # Для завершенных задач устанавливаем completed=1, total=1
        # Для processing задач НЕ устанавливаем completed/total, чтобы спиннер продолжал работать
        if task.status in [TaskStatus.COMPLETED, TaskStatus.ERROR, TaskStatus.SKIPPED]:
            update_params.update({"completed": 1, "total": 1})
        elif task.status == TaskStatus.PROCESSING:
            # Для processing задач убираем total, чтобы спиннер работал
            update_params.update({"total": None})

        self.progress.update(task_id, **update_params)

    def hide_all_file_tasks(self):
        """Скрывает все оставшиеся видимые задачи файлов."""
        if not self.progress:
            return
        for task_id in self._visible_file_tasks:
            self.progress.update(task_id, visible=False)
        self._visible_file_tasks.clear()

    def display_final_report(
        self, orchestrator: BaseTaskOrchestrator, elapsed_time: float
    ):
        """Отображает итоговый отчет."""
        summary_data = orchestrator.get_summary_data()
        stats = summary_data["stats"]

        summary_table = Table(show_header=False, box=None, padding=(0, 2))
        summary_table.add_column()
        summary_table.add_column(style="bold", justify="right")

        summary_table.add_row(
            "Всего операций успешно:", f"[green]{stats['success']}[/green]"
        )
        summary_table.add_row(
            "Всего операций с ошибками:", f"[red]{stats['errors']}[/red]"
        )
        summary_table.add_row(
            "Всего операций пропущено:", f"[yellow]{stats['skipped']}[/yellow]"
        )
        summary_table.add_row(
            "Всего файлов в папке:", f"[cyan]{summary_data['total_files']}[/cyan]"
        )
        summary_table.add_row(
            "Затрачено времени:", f"[yellow]{format_time(elapsed_time)}[/yellow]"
        )

        final_panel_content = [summary_table]

        # Добавляем таблицу ошибок, если есть
        if stats["details"]:
            errors_table = Table(
                title="[bold red]Детали ошибок[/bold red]", expand=True
            )
            errors_table.add_column("Файл", style="cyan", no_wrap=True)
            errors_table.add_column("Ошибка", style="red")
            for filename, error_msg in stats["details"]:
                errors_table.add_row(filename, error_msg)
            final_panel_content.append(errors_table)

        renderable_group = Group(*final_panel_content)
        self.console.print(
            Panel(
                renderable_group,
                title="[bold green]✅ Обработка завершена[/bold green]",
                border_style="blue",
                padding=(1, 2),
            )
        )

        if stats["errors"] > 0:
            self.console.print(
                "[yellow]Для получения подробной информации об ошибках, проверьте лог-файл.[/yellow]"
            )
