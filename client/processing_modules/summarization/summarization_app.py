# processing_modules/summarization/summarization_app.py

import asyncio
from pathlib import Path
from typing import List, Any

import httpx
import typer
from loguru import logger
from rich.console import Console

from client.processing_modules.summarization.summarize_modules.config import AppConfig
from client.processing_modules.universal_interface import UniversalInterface
from client.processing_modules.summarization.summarize_modules.api_client_http import (
    APIClient,
)
from client.processing_modules.summarization.summarize_modules.file_processor import (
    FileProcessor,
)
from client.processing_modules.summarization.summarize_modules.polling_strategy import (
    PollingSummarizationStrategy,
)
from client.processing_modules.summarization.summarize_modules.sse_strategy import (
    SSESummarizationStrategy,
)
from client.processing_modules.summarization.summarize_modules.exceptions import (
    ServerUnavailableError,
    AuthenticationError,
    APITimeoutError,
)
from client.processing_modules.summarization.summarization_orchestrator import (
    SummarizationOrchestrator,
)


class SummarizationApp:
    """Приложение суммаризации, адаптированное под новую архитектуру."""

    def __init__(self, config: AppConfig, directory: Path, modes_to_run: List[str]):
        self.config = config
        self.directory = directory
        self.modes_to_run = modes_to_run
        self.console = Console()
        self.interface = UniversalInterface(self.console)

        # Компоненты суммаризации
        self.api_client: APIClient | None = None
        self.file_processor: FileProcessor | None = None
        self.summarization_strategy: Any = None
        self.orchestrator: SummarizationOrchestrator | None = None

    def _create_dependencies(self):
        """Инициализирует и настраивает зависимые компоненты."""
        self.api_client = APIClient(
            host=self.config.host,
            port=self.config.port,
            api_key=self.config.api_key,
            debug=self.config.debug,
        )
        self.file_processor = FileProcessor(
            format_markdown=self.config.format_md, console=self.console
        )

        if self.config.strategy == "sse":
            self.summarization_strategy = SSESummarizationStrategy(
                api_client=self.api_client,
                console=self.console,
                debug=self.config.debug,
            )
            self.console.print(
                "[bold green]✅ Используется SSE стратегия суммаризации.[/bold green]"
            )
        else:
            self.summarization_strategy = PollingSummarizationStrategy(
                api_client=self.api_client,
                console=self.console,
                debug=self.config.debug,
            )
            self.console.print(
                "[bold yellow]⚠️ Используется устаревшая стратегия суммаризации (Polling).[/bold yellow]"
            )

        self.orchestrator = SummarizationOrchestrator(
            directory=self.directory,
            modes_to_run=self.modes_to_run,
            file_processor=self.file_processor,
            summarization_strategy=self.summarization_strategy,
        )

    async def run(self):
        """Запускает основной асинхронный процесс обработки."""
        logger.info("Summarization application started.")
        self._create_dependencies()

        # Отображаем заголовок и план
        self.interface.display_task_header(self.orchestrator)
        self.interface.display_processing_plan(self.orchestrator)

        # Проверяем наличие файлов
        txt_files = self.orchestrator.discover_files()
        if not txt_files:
            self.console.print(
                "[yellow]Предупреждение: .txt файлы в директории не найдены.[/yellow]"
            )
            return

        try:
            typer.confirm("\nНачать обработку?", abort=True, default=True)
        except typer.Abort:
            self.console.print("[red]Отменено пользователем.[/red]")
            logger.info("Operation aborted by user.")
            return

        start_time = asyncio.get_event_loop().time()

        try:
            # Проверяем подключение к серверу
            await self._connect_to_server()

            with self.interface.create_progress_bar() as progress:
                total_operations = len(txt_files) * len(self.modes_to_run)
                overall_task_id = progress.add_task(
                    "[bold blue]Общий прогресс[/bold blue]",
                    total=total_operations,
                    status="",
                )

                # Обрабатываем все режимы
                results = await self.orchestrator.process_all_modes(
                    progress_callback=lambda task,
                    current_op,
                    total_ops,
                    mode,
                    mode_idx: self._progress_callback(
                        progress,
                        overall_task_id,
                        task,
                        current_op,
                        total_ops,
                        mode,
                        mode_idx,
                    )
                )

                # Отображаем результаты по режимам
                for mode_result in results["mode_results"]:
                    self._display_mode_summary(mode_result)

                self.interface.hide_all_file_tasks()
                progress.update(
                    overall_task_id,
                    description="[bold green]Все режимы обработаны[/bold green]",
                    status="✅",
                )

        except (
            ServerUnavailableError,
            AuthenticationError,
            httpx.RequestError,
            APITimeoutError,
        ) as e:
            self.console.print(
                f"\n[bold red]Критическая ошибка связи с сервером: {e}[/bold red]"
            )
            logger.critical(f"A critical connection error occurred: {e}")
        except KeyboardInterrupt:
            self.console.print("\n\n[yellow]Прервано пользователем.[/yellow]")
            logger.warning("Operation interrupted by user (KeyboardInterrupt).")
        finally:
            elapsed_time = asyncio.get_event_loop().time() - start_time
            self.interface.display_final_report(self.orchestrator, elapsed_time)
            logger.info("Summarization application finished.")

    async def _connect_to_server(self):
        """Проверяет доступность сервера."""
        logger.info("Attempting to connect to the server...")
        if not await self.api_client.check_server_available():
            message = (
                f"Не удалось подключиться к серверу по адресу "
                f"{self.api_client.base_url}. Пожалуйста, запустите сервер."
            )
            logger.critical(message)
            raise ServerUnavailableError(message)
        logger.info("Server connection successful.")

    def _progress_callback(
        self, progress, overall_task_id, task, current_op, total_ops, mode, mode_idx
    ):
        """Колбэк для обновления прогресса."""
        # Обновляем общий прогресс
        progress.update(
            overall_task_id,
            description=f"[bold blue]Общий прогресс (Режим {mode_idx + 1}/{len(self.modes_to_run)}: {mode})[/bold blue]",
            completed=current_op,
        )

        # Добавляем задачу файла, если еще не добавлена
        if not hasattr(task, "_progress_task_id"):
            task._progress_task_id = self.interface.add_file_task(
                f"В очереди: {task.get_display_name()}"
            )

        # Обновляем задачу файла
        self.interface.update_file_task(task._progress_task_id, task)

        # Обновляем общий прогресс после обработки файла
        progress.update(overall_task_id, advance=1)

    def _display_mode_summary(self, mode_result):
        """Выводит итоги по одному режиму."""
        mode = mode_result["mode"]
        stats = mode_result["stats"]
        self.console.print(
            f"[bold]Итоги по режиму '{mode}':[/bold] "
            f"[green]Успешно: {stats['success']}[/green], "
            f"[yellow]Пропущено: {stats['skipped']}[/yellow], "
            f"[red]С ошибками: {stats['errors']}[/red]"
        )
        self.console.print("-" * 30)
