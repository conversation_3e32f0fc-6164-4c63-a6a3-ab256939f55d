# api_cl.py
import json

import httpx  # Замена websockets на httpx

from .exceptions import AuthenticationError, APITimeoutError, ServerUnavailableError
from loguru import logger


class APIClient:
    """Клиент для работы с API сервера суммаризации через HTTP с асинхронным опросом."""

    def __init__(
        self,
        host: str = "localhost",
        port: int = 8000,
        api_key: str | None = None,
        debug: bool = False,
    ):
        self.host = host
        self.port = port
        self.base_url = f"http://{self.host}:{self.port}"
        self.api_key = api_key
        self.debug = debug
        # self.console = Console() # Removed
        # self.server_process = None # Removed
        self.request_timeout = 5  # Individual HTTP request timeout
        # self.operation_timeout = 300  # Removed
        # self.polling_interval = 2 # Removed

        logger.debug(
            f"APIClient initialized for HTTP Polling. Host: {self.host}, Port: {self.port}, API Key: {'***' if self.api_key else 'None'}"
        )

    def _log_debug(self, message: str, exc_info: bool = False):
        """Логирование отладочных сообщений"""
        if self.debug:
            if exc_info:
                logger.opt(exception=True).debug(f"[APIClient] {message}")
            else:
                logger.debug(f"[APIClient] {message}")

    async def check_server_available(self) -> bool:
        """Проверка доступности сервера через GET запрос к эндпоинту /ping"""
        logger.debug("Checking server availability...")
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/ping", timeout=5)
                response_json = response.json()
                logger.debug(
                    f"Server check response status: {response.status_code}, message: {response_json}"
                )
                if (
                    response.status_code == 200
                    and response_json.get("message") == "pong"
                ):
                    logger.debug("Server is available and responded with 'pong'.")
                    return True
                else:
                    logger.debug(
                        "Server responded, but not with expected status or message."
                    )
                    return False
        except (httpx.RequestError, json.JSONDecodeError) as e:
            logger.debug(f"Server check failed or server not running: {e}")
            return False

    # Removed start_server method

    def cleanup(self):
        """Очистка ресурсов при завершении"""
        logger.debug("Cleanup called.")
        # Removed server_process termination logic

    # def get_sse_url(self, task_id: str) -> str:
    #     """Constructs the SSE endpoint URL for a given task ID."""
    #     return f"{self.base_url}/tasks/{task_id}/sse"

    def _get_auth_headers(self) -> dict:
        """Helper to get authentication headers."""
        headers = {}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
            logger.debug("Authorization header added.")
        else:
            logger.debug("No API key provided, no Authorization header added.")
        return headers

    async def initiate_summarization_task(self, text: str, mode: str) -> dict:
        logger.debug(
            f"Initiating summarization request with mode: {mode}, text_length: {len(text)}"
        )
        payload = {"text": text, "mode": mode}
        headers = self._get_auth_headers()
        try:
            async with httpx.AsyncClient(timeout=self.request_timeout) as client:
                response = await client.post(
                    f"{self.base_url}/summarize/", json=payload, headers=headers
                )
                logger.debug(
                    f"Summarization task initiation response status: {response.status_code}, content: {response.text}"
                )
                response.raise_for_status()  # Raise an exception for HTTP errors
                return response.json()
        except httpx.ConnectError as e:
            logger.error(f"Server unavailable during task initiation: {e}")
            raise ServerUnavailableError(f"Сервер недоступен: {e}") from e
        except httpx.TimeoutException as e:
            logger.error(f"Timeout during task initiation: {e}")
            raise APITimeoutError(
                f"Время ожидания ответа от сервера истекло: {e}"
            ) from e
        except httpx.RequestError as e:
            if response.status_code in (401, 403):
                logger.error(
                    f"Authentication error during task initiation (status {response.status_code}): {response.text}"
                )
                raise AuthenticationError(f"Ошибка авторизации: {response.text}") from e
            logger.error(f"Request error during task initiation: {e}")
            raise

    async def get_task_status(self, status_url: str) -> dict:
        logger.debug(f"Fetching task status from: {status_url}")
        headers = self._get_auth_headers()
        try:
            async with httpx.AsyncClient(timeout=self.request_timeout) as client:
                response = await client.get(status_url, headers=headers)
                logger.debug(
                    f"Task status response for {status_url}: {response.status_code}, content: {response.text}"
                )
                response.raise_for_status()
                return response.json()
        except httpx.ConnectError as e:
            logger.error(
                f"Server unavailable when fetching status from {status_url}: {e}"
            )
            raise ServerUnavailableError(
                f"Сервер недоступен при запросе статуса: {e}"
            ) from e
        except httpx.TimeoutException as e:
            logger.error(f"Timeout when fetching task status from {status_url}: {e}")
            raise APITimeoutError(f"Время ожидания статуса задачи истекло: {e}") from e
        except httpx.RequestError as e:
            if response.status_code in (401, 403):
                logger.error(
                    f"Authentication error when fetching status (status {response.status_code}) from {status_url}: {response.text}"
                )
                raise AuthenticationError(
                    f"Ошибка авторизации при запросе статуса: {response.text}"
                ) from e
            logger.error(f"Request error when fetching status from {status_url}: {e}")
            raise

    async def get_task_result(self, result_url: str) -> dict:
        logger.debug(f"Fetching task result from: {result_url}")
        headers = self._get_auth_headers()
        try:
            async with httpx.AsyncClient(
                timeout=120.0
            ) as client:  # Long timeout for result fetching
                response = await client.get(result_url, headers=headers)
                logger.debug(
                    f"Task result response for {result_url}: {response.status_code}, content: {response.text}"
                )
                response.raise_for_status()
                return response.json()
        except httpx.ConnectError as e:
            logger.error(
                f"Server unavailable when fetching result from {result_url}: {e}"
            )
            raise ServerUnavailableError(
                f"Сервер недоступен при запросе результата: {e}"
            ) from e
        except httpx.TimeoutException as e:
            logger.error(f"Timeout when fetching task result from {result_url}: {e}")
            raise APITimeoutError(
                f"Время ожидания результата задачи истекло: {e}"
            ) from e
        except httpx.RequestError as e:
            if response.status_code in (401, 403):
                logger.error(
                    f"Authentication error when fetching result (status {response.status_code}) from {result_url}: {response.text}"
                )
                raise AuthenticationError(
                    f"Ошибка авторизации при запросе результата: {response.text}"
                ) from e
            logger.error(f"Request error when fetching result from {result_url}: {e}")
            raise
