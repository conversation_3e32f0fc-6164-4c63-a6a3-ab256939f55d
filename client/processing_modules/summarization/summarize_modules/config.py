from dataclasses import dataclass
from enum import Enum

# --- Глобальные константы ---
DEFAULT_HOST = "localhost"
DEFAULT_PORT = 8001
LOG_FILE = "summarizers.log"


class StrategyEnum(str, Enum):
    """Перечисление доступных стратегий взаимодействия с API."""

    sse = "sse"
    polling = "polling"


@dataclass(frozen=True)
class AppConfig:
    """Конфигурация приложения, создаваемая из аргументов командной строки."""

    host: str
    port: int
    api_key: str | None
    strategy: str
    format_md: bool
    debug: bool
