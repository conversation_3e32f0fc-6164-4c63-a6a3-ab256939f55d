import asyncio
from pathlib import Path
from typing import Any, Dict

import httpx
from rich.console import Console
from rich.progress import Progress
from loguru import logger

from .api_client_http import APIClient
from .exceptions import (
    AuthenticationError,
    APITimeoutError,
    TaskFailedError,
    SummarizationError,
    ServerUnavailableError,
)
from .interfaces import ISummarizationStrategy


class SSESummarizationStrategy(ISummarizationStrategy):
    def __init__(
        self,
        api_client: APIClient,
        console: Console,
        debug: bool = False,
        sse_timeout: float = 300,  # Max duration to wait for SSE events
        # ИЗМЕНЕНИЕ: Устанавливаем более разумные значения по умолчанию для retries.
        # Это повышает устойчивость к временным сбоям сети или долгой обработке на сервере.
        max_retries: int = 2,
        retry_delay: float = 1.0,
    ):
        self.api_client = api_client
        self.console = console
        self.debug = debug
        self.sse_timeout = sse_timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        if self.debug:
            logger.debug(
                f"SSESummarizationStrategy initialized in debug mode (Retries: {self.max_retries}, Delay: {self.retry_delay}s)."
            )

    async def process_file(
        self,
        file_path: Path,
        mode: str,
        text_content: str,
        progress: Progress = None,
        task_id: Any = None,
    ) -> Dict[str, Any]:
        file_name = file_path.name
        logger.debug(
            f"Starting process_file for {file_name} with mode '{mode}' and text length {len(text_content)}."
        )
        if progress and task_id is not None:
            progress.update(
                task_id,
                description=f"[bold white]Отправка ({mode}, SSE): {file_name}[/bold white]",
                status="📤",
            )

        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    logger.warning(
                        f"Retrying SSE operation for {file_name} (attempt {attempt}/{self.max_retries}, delay {self.retry_delay}s)..."
                    )
                    await asyncio.sleep(self.retry_delay)

                logger.debug(
                    f"Initiating summarization task for {file_name} (attempt {attempt + 1})..."
                )
                start_response = await self.api_client.initiate_summarization_task(
                    text=text_content, mode=mode
                )
                logger.debug(
                    f"Initiate task response for {file_name}: {start_response}"
                )

                if start_response.get("status") == "completed" and start_response.get(
                    "result_url"
                ):
                    logger.debug(
                        f"Task for {file_name} instantly completed (cached). Fetching result."
                    )
                    if progress and task_id is not None:
                        progress.update(
                            task_id,
                            description=f"[green]Готово (кеш): {file_name}[/green]",
                            status="✅",
                            completed=1,
                            total=1,
                        )
                    final_result = await self.api_client.get_task_result(
                        start_response["result_url"]
                    )
                    summary = final_result.get("result", {}).get("summary")
                    if not summary:
                        logger.error(
                            f"Instant result for {file_name} did not contain valid summary. Result: {final_result}"
                        )
                        raise ValueError(
                            f"Instant result for {file_name} did not contain valid summary."
                        )
                    logger.debug(
                        f"Instant summary retrieved for {file_name}. Summary length: {len(summary)}."
                    )
                    return {"status": "completed", "summary": summary, "error": None}

                sse_url_from_server = start_response.get("sse_url")
                if not sse_url_from_server:
                    raise ValueError(
                        f"Server response for {file_name} did not include 'sse_url'. Response: {start_response}"
                    )

                sse_url = sse_url_from_server
                logger.debug(f"Confirmed SSE URL for task {file_name}: {sse_url}")

                logger.debug(
                    f"Connecting to SSE stream for {file_name} with timeout {self.sse_timeout}s."
                )
                result_url = None

                async with httpx.AsyncClient(timeout=self.sse_timeout) as client:
                    headers = {}
                    if self.api_client.api_key:
                        headers["Authorization"] = f"Bearer {self.api_client.api_key}"

                    async with client.stream(
                        "GET", sse_url, headers=headers, timeout=None
                    ) as response:
                        if response.status_code in (401, 403):
                            raise AuthenticationError(
                                f"SSE Authentication failed for {file_name}: {response.text}"
                            )
                        response.raise_for_status()

                        buffer = ""
                        async for chunk in response.aiter_bytes():
                            buffer += chunk.decode("utf-8")
                            while "\n\n" in buffer:
                                event_block, buffer = buffer.split("\n\n", 1)
                                event_data = self._parse_sse_event(
                                    event_block, file_name
                                )

                                if not event_data:
                                    continue

                                status = event_data.get("status")
                                self._update_progress(
                                    progress, task_id, status, file_name
                                )

                                if status == "completed":
                                    result_url = event_data.get("result_url")
                                    logger.success(
                                        f"SSE: Task for {file_name} completed. Result URL found: {result_url}"
                                    )
                                    break  # <--- ВЫХОДИМ ИЗ ЦИКЛА НЕМЕДЛЕННО

                                if status == "failed":
                                    error_msg = (
                                        event_data.get("error") or "Unknown SSE error"
                                    )
                                    return {
                                        "status": "failed",
                                        "summary": None,
                                        "error": error_msg,
                                    }
                            if (
                                result_url
                            ):  # Проверяем после каждого обработанного чанка
                                break

                # После выхода из цикла (по break или по завершению потока)
                if result_url:
                    logger.info(
                        f"Fetching final result for {file_name} from {result_url}"
                    )
                    final_result = await self.api_client.get_task_result(result_url)
                    summary = final_result.get("result", {}).get("summary")
                    if not summary:
                        raise SummarizationError(
                            f"Could not retrieve summary from final result URL for {file_name}."
                        )
                    return {"status": "completed", "summary": summary, "error": None}

                # Если мы здесь, значит, цикл завершился без `completed`
                logger.warning(
                    f"SSE stream for {file_name} closed without a 'completed' status. Retrying if possible."
                )
                raise APITimeoutError(
                    f"SSE stream for {file_name} closed prematurely without completion."
                )

            except (httpx.RequestError, ServerUnavailableError, APITimeoutError) as e:
                logger.debug(
                    f"Caught transient exception for {file_name} on attempt {attempt + 1}/{self.max_retries + 1}: {type(e).__name__} - {e.args[0]}"
                )
                if attempt < self.max_retries:
                    logger.warning(
                        f"Transient error for {file_name}: {e}. Retrying in {self.retry_delay}s..."
                    )
                else:
                    logger.error(
                        f"Max retries ({self.max_retries}) exhausted for {file_name}. Giving up. Last error: {e}"
                    )
                    self.console.print(
                        f"[red]Ошибка обработки {file_name}: {e.args[0]}[/red]"
                    )
                    raise

            except AuthenticationError:
                logger.error(f"Authentication error for {file_name}. Re-raising.")
                raise
            except (TaskFailedError, ValueError, SummarizationError) as e:
                logger.error(
                    f"Operational/data error for {file_name}: {type(e).__name__} - {e.args[0]}. Re-raising."
                )
                self.console.print(
                    f"[red]Ошибка обработки {file_name}: {e.args[0]}[/red]"
                )
                raise
            except Exception as e:
                logger.exception(
                    f"Unhandled exception during SSE file processing for {file_name}."
                )
                self.console.print(
                    f"[red]Неизвестная ошибка при обработке {file_name} через SSE: {e}[/red]"
                )
                raise

        logger.warning(
            f"Exiting process_file for {file_name} after all retries. No valid SSE events received."
        )
        return {
            "status": "failed",
            "summary": None,
            "error": "No valid SSE events received after retries",
        }
