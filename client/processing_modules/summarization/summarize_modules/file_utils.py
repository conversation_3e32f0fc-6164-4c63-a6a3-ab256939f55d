def trim_filename(filename: str, max_length: int = 50, ellipsis: str = "...") -> str:
    """Обрезает имя файла до указанной длины, добавляя многоточие при необходимости.
    Гарантирует, что возвращаемая строка всегда имеет длину max_length,
    дополняя пробелами справа, если она короче.
    """
    if len(filename) <= max_length:
        result = filename
    else:
        # Оставляем расширение файла
        name, ext = filename.rsplit(".", 1) if "." in filename else (filename, "")
        ext = f".{ext}" if ext else ""

        # Вычисляем доступную длину для имени с учетом расширения и многоточия
        available_length = max_length - len(ext) - len(ellipsis)
        if available_length <= 0:
            # На крайний случай, если max_length слишком мал, просто обрезаем
            result = filename[:max_length]
        else:
            # Обрезаем имя и добавляем многоточие и расширение
            result = f"{name[:available_length]}{ellipsis}{ext}"

    # Добиваем пробелами до нужной длины
    return result.ljust(max_length)
