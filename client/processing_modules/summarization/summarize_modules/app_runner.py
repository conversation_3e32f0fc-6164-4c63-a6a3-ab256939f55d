# summarize_modules/app_runner.py

import asyncio
from pathlib import Path
from typing import List, Any

import httpx
import typer
from loguru import logger
from rich.console import Console

from .config import AppConfig
from .console_manager import ConsoleManager
from .file_summarizer import FileSummarizer
from .api_client_http import APIClient
from .file_processor import FileProcessor
from .polling_strategy import PollingSummarizationStrategy
from .sse_strategy import SSESummarizationStrategy
from .exceptions import ServerUnavailableError, AuthenticationError, APITimeoutError


class SummarizationApp:
    """Оркестратор процесса суммаризации."""

    def __init__(self, config: AppConfig, directory: Path, modes_to_run: List[str]):
        self.config = config
        self.directory = directory
        self.modes_to_run = modes_to_run
        self.console = Console()
        self.console_manager = ConsoleManager(self.console)
        self.api_client: APIClient | None = None
        self.file_processor: FileProcessor | None = None
        self.summarization_strategy: Any = None
        self.summarizer: FileSummarizer | None = None

    def _create_dependencies(self):
        """Инициализирует и настраивает зависимые компоненты."""
        self.api_client = APIClient(
            host=self.config.host,
            port=self.config.port,
            api_key=self.config.api_key,
            debug=self.config.debug,
        )
        self.file_processor = FileProcessor(
            format_markdown=self.config.format_md, console=self.console
        )
        if self.config.strategy == "sse":
            self.summarization_strategy = SSESummarizationStrategy(
                api_client=self.api_client,
                console=self.console,
                debug=self.config.debug,
            )
            self.console.print(
                "[bold green]✅ Используется SSE стратегия суммаризации.[/bold green]"
            )
        else:
            self.summarization_strategy = PollingSummarizationStrategy(
                api_client=self.api_client,
                console=self.console,
                debug=self.config.debug,
            )
            self.console.print(
                "[bold yellow]⚠️ Используется устаревшая стратегия суммаризации (Polling).[/bold yellow]"
            )

        self.summarizer = FileSummarizer(
            self.api_client,
            self.summarization_strategy,
            self.file_processor,
            self.console_manager,
        )

    async def run(self):
        """Запускает основной асинхронный процесс обработки."""
        logger.info("Application started.")
        self._create_dependencies()

        txt_files = sorted(list(self.directory.glob("**/*.txt")))
        if not txt_files:
            self.console.print(
                "[yellow]Предупреждение: .txt файлы в директории не найдены.[/yellow]"
            )
            return

        self.console_manager.display_plan(
            self.directory, self.modes_to_run, len(txt_files)
        )
        try:
            typer.confirm("\nНачать обработку?", abort=True, default=True)
        except typer.Abort:
            self.console.print("[red]Отменено пользователем.[/red]")
            logger.info("Operation aborted by user.")
            return

        overall_stats = {"success": 0, "errors": 0, "skipped": 0, "details": []}
        start_time = asyncio.get_event_loop().time()

        try:
            await self.summarizer.connect_to_server()

            with self.console_manager.create_progress_bar() as progress:
                total_operations = len(txt_files) * len(self.modes_to_run)
                overall_task_id = progress.add_task(
                    "[bold blue]Общий прогресс[/bold blue]",
                    total=total_operations,
                    status="",
                )

                for i, mode_name in enumerate(self.modes_to_run):
                    progress.update(
                        overall_task_id,
                        description=f"[bold blue]Общий прогресс (Режим {i + 1}/{len(self.modes_to_run)}: {mode_name})[/bold blue]",
                    )

                    s, e, sk, details, mode = await self.summarizer.process_directory(
                        self.directory, mode_name, progress, overall_task_id
                    )
                    overall_stats["success"] += s
                    overall_stats["errors"] += e
                    overall_stats["skipped"] += sk
                    overall_stats["details"].extend(details)
                    self.console_manager.display_mode_summary(
                        {"mode": mode, "success": s, "errors": e, "skipped": sk}
                    )

                self.console_manager.hide_all_file_tasks()
                progress.update(
                    overall_task_id,
                    description="[bold green]Все режимы обработаны[/bold green]",
                    status="✅",
                )

        except (
            ServerUnavailableError,
            AuthenticationError,
            httpx.RequestError,
            APITimeoutError,
        ) as e:
            self.console.print(
                f"\n[bold red]Критическая ошибка связи с сервером: {e}[/bold red]"
            )
            logger.critical(f"A critical connection error occurred: {e}")
        except KeyboardInterrupt:
            self.console.print("\n\n[yellow]Прервано пользователем.[/yellow]")
            logger.warning("Operation interrupted by user (KeyboardInterrupt).")
        finally:
            elapsed_time = asyncio.get_event_loop().time() - start_time
            self.console_manager.display_final_report(
                overall_stats, len(txt_files), elapsed_time
            )
            logger.info("Application finished.")
