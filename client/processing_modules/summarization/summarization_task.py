# processing_modules/summarization/summarization_task.py

from pathlib import Path
from typing import Dict, Any, Optional

from loguru import logger

from client.processing_modules.base_classes import BaseFileTask, TaskResult, TaskStatus
from client.processing_modules.summarization.summarize_modules.exceptions import (
    AuthenticationError,
    APITimeoutError,
    TaskFailedError,
    FileProcessingError,
)


class SummarizationTask(BaseFileTask):
    """Задача суммаризации файла."""

    def __init__(self, file_path: Path, task_params: Dict[str, Any]):
        super().__init__(file_path, task_params)
        self.mode = task_params.get("mode", "default")
        self.file_processor = task_params.get("file_processor")
        self.summarization_strategy = task_params.get("summarization_strategy")

    def _check_skip_conditions(self) -> Optional[str]:
        """Проверяет условия пропуска файла."""
        if not self.file_processor:
            return None

        # Проверяем, существует ли уже суммаризация для данного файла и режима
        if self.file_processor.does_summary_exist(self.file_path, self.mode):
            # Определяем имя файла суммаризации
            if self.mode == "default":
                summary_file = self.file_path.with_suffix(".md")
            else:
                summary_file = self.file_path.with_suffix(f"_{self.mode}.md")

            return f"есть {summary_file.name}"

        return None

    async def process(self) -> TaskResult:
        """Обрабатывает файл для суммаризации."""
        try:
            # Читаем содержимое файла
            text_content = self.file_processor.read_file(self.file_path)

            # Обрабатываем через стратегию суммаризации
            result = await self.summarization_strategy.process_file(
                file_path=self.file_path,
                mode=self.mode,
                text_content=text_content,
                progress=None,  # Прогресс будет управляться на уровне оркестратора
                task_id=None,
            )

            if result["status"] == "completed" and result["summary"]:
                # Сохраняем результат
                self.file_processor.save_summary(
                    result["summary"], self.file_path, self.mode
                )
                return TaskResult(
                    status=TaskStatus.COMPLETED,
                    message=f"Суммаризация завершена для режима '{self.mode}'",
                    data={"summary": result["summary"]},
                )
            elif result["status"] == "failed":
                error_msg = result.get("error", "Неизвестная ошибка")
                return TaskResult(status=TaskStatus.ERROR, error=error_msg)
            else:
                return TaskResult(
                    status=TaskStatus.SKIPPED,
                    message="Файл пропущен - нет валидного ответа",
                )

        except AuthenticationError as e:
            logger.critical(f"Authentication error for {self.file_path.name}: {e}")
            raise  # Пробрасываем критические ошибки аутентификации
        except (
            APITimeoutError,
            TaskFailedError,
            FileProcessingError,
            ValueError,
        ) as e:
            logger.error(f"Error processing {self.file_path.name}: {e}")
            return TaskResult(status=TaskStatus.ERROR, error=str(e))
        except Exception as e:
            logger.exception(
                f"Unhandled exception while processing {self.file_path.name}"
            )
            return TaskResult(
                status=TaskStatus.ERROR, error=f"Неожиданная ошибка: {str(e)}"
            )

    def get_display_name(self) -> str:
        """Возвращает сокращенное имя файла для отображения."""
        return self.file_path.name
