# processing_modules/summarization/summarization_orchestrator.py

from pathlib import Path
from typing import Dict, Any, List

from client.processing_modules.base_classes import BaseTaskOrchestrator, BaseFileTask
from client.processing_modules.summarization.summarization_task import SummarizationTask


class SummarizationOrchestrator(BaseTaskOrchestrator):
    """Оркестратор для задач суммаризации."""

    def __init__(self, directory: Path, modes_to_run: List[str], **kwargs):
        # Создаем task_params с дополнительными параметрами
        task_params = {"modes_to_run": modes_to_run, **kwargs}
        super().__init__(directory, task_params)
        self.modes_to_run = modes_to_run
        self.current_mode = None
        self.current_mode_index = 0

        # Сохраняем компоненты суммаризации
        self.file_processor = kwargs.get("file_processor")
        self.summarization_strategy = kwargs.get("summarization_strategy")

    def get_task_name(self) -> str:
        """Возвращает название типа задачи."""
        return "Суммаризация текстовых файлов"

    def get_task_description(self) -> str:
        """Возвращает описание задачи."""
        modes_str = ", ".join(self.modes_to_run)
        return f"Суммаризация в режимах: {modes_str}"

    def get_file_pattern(self) -> str:
        """Возвращает паттерн для поиска файлов."""
        return "**/*.txt"

    def create_file_task(self, file_path: Path) -> BaseFileTask:
        """Создает задачу суммаризации для конкретного файла."""
        task_params = {
            "mode": self.current_mode,
            "file_processor": self.file_processor,
            "summarization_strategy": self.summarization_strategy,
        }
        return SummarizationTask(file_path, task_params)

    def set_current_mode(self, mode: str):
        """Устанавливает текущий режим обработки."""
        self.current_mode = mode

    async def process_all_modes(self, progress_callback=None) -> Dict[str, Any]:
        """Обрабатывает все файлы для всех режимов."""
        overall_stats = {"success": 0, "errors": 0, "skipped": 0, "details": []}
        mode_results = []

        files = self.discover_files()
        total_operations = len(files) * len(self.modes_to_run)
        current_operation = 0

        for mode_index, mode in enumerate(self.modes_to_run):
            self.current_mode_index = mode_index
            self.set_current_mode(mode)

            # Создаем задачи для текущего режима
            self.create_tasks()

            # Обрабатываем задачи с колбэком прогресса
            mode_stats = await self.process_all_tasks(
                progress_callback=lambda task, i, total: progress_callback(
                    task, current_operation + i, total_operations, mode, mode_index
                )
                if progress_callback
                else None
            )

            # Обновляем общую статистику
            overall_stats["success"] += mode_stats["success"]
            overall_stats["errors"] += mode_stats["errors"]
            overall_stats["skipped"] += mode_stats["skipped"]
            overall_stats["details"].extend(mode_stats["details"])

            # Сохраняем результаты режима
            mode_results.append({"mode": mode, "stats": mode_stats})

            current_operation += len(files)

        return {
            "overall_stats": overall_stats,
            "mode_results": mode_results,
            "total_files": len(files),
            "total_operations": total_operations,
        }
