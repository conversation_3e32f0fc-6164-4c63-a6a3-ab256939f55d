# processing_modules/test_processing/test_app.py

import asyncio
from pathlib import Path

import typer
from loguru import logger
from rich.console import Console

from client.processing_modules.universal_interface import UniversalInterface
from client.processing_modules.base_classes import TaskStatus
from client.processing_modules.test_processing.test_orchestrator import (
    TestProcessingOrchestrator,
)


class TestProcessingApp:
    """Приложение для тестовой обработки файлов."""

    def __init__(self, directory: Path, **kwargs):
        self.directory = directory
        self.console = Console()
        self.interface = UniversalInterface(self.console)
        self.orchestrator = TestProcessingOrchestrator(directory, **kwargs)

    async def run(self):
        """Запускает основной процесс тестовой обработки."""
        logger.info("Test processing application started.")

        # Отображаем заголовок и план
        self.interface.display_task_header(self.orchestrator)
        self.interface.display_processing_plan(self.orchestrator)

        # Проверяем наличие файлов
        files = self.orchestrator.discover_files()
        if not files:
            self.console.print(
                "[yellow]Предупреждение: файлы для обработки не найдены.[/yellow]"
            )
            return

        # Автоматически начинаем обработку в неинтерактивном режиме
        auto_start = self.orchestrator.task_params.get("auto_start", False)
        if not auto_start:
            try:
                typer.confirm("\nНачать обработку?", abort=True, default=True)
            except typer.Abort:
                self.console.print("[red]Отменено пользователем.[/red]")
                logger.info("Operation aborted by user.")
                return

        start_time = asyncio.get_event_loop().time()

        try:
            with self.interface.create_progress_bar() as progress:
                total_files = len(files)
                overall_task_id = progress.add_task(
                    "[bold blue]Общий прогресс[/bold blue]",
                    total=total_files,
                    status="",
                )

                # Обрабатываем все файлы
                await self.orchestrator.process_all_tasks(
                    progress_callback=lambda task, i, total: self._progress_callback(
                        progress, overall_task_id, task, i, total
                    )
                )

                self.interface.hide_all_file_tasks()
                progress.update(
                    overall_task_id,
                    description="[bold green]Обработка завершена[/bold green]",
                    status="✅",
                )

        except KeyboardInterrupt:
            self.console.print("\n\n[yellow]Прервано пользователем.[/yellow]")
            logger.warning("Operation interrupted by user (KeyboardInterrupt).")
        finally:
            elapsed_time = asyncio.get_event_loop().time() - start_time
            self.interface.display_final_report(self.orchestrator, elapsed_time)
            logger.info("Test processing application finished.")

    def _progress_callback(self, progress, overall_task_id, task, current_index, total):
        """Колбэк для обновления прогресса."""
        # Добавляем задачу файла, если еще не добавлена
        if not hasattr(task, "_progress_task_id"):
            task._progress_task_id = self.interface.add_file_task(
                f"В очереди: {task.get_display_name()}"
            )

        # Обновляем задачу файла
        self.interface.update_file_task(task._progress_task_id, task)

        # Обновляем общий прогресс только для завершенных задач
        if task.status in [TaskStatus.COMPLETED, TaskStatus.ERROR, TaskStatus.SKIPPED]:
            progress.update(overall_task_id, advance=1)
