# processing_modules/test_processing/test_orchestrator.py

from pathlib import Path

from client.processing_modules.base_classes import BaseTaskOrchestrator, BaseFileTask
from client.processing_modules.test_processing.test_task import TestProcessingTask


class TestProcessingOrchestrator(BaseTaskOrchestrator):
    """Оркестратор для тестовой обработки файлов."""

    def __init__(self, directory: Path, **kwargs):
        super().__init__(directory, kwargs)
        self.processing_time = kwargs.get("processing_time", 1.0)
        self.simulate_errors = kwargs.get("simulate_errors", False)
        self.error_probability = kwargs.get("error_probability", 0.1)
        self.skip_large_files = kwargs.get("skip_large_files", False)
        self.max_file_size = kwargs.get("max_file_size", 1000)
        self.file_pattern = kwargs.get("file_pattern", "**/*.txt")

    def get_task_name(self) -> str:
        """Возвращает название типа задачи."""
        return "Тестовая обработка файлов"

    def get_task_description(self) -> str:
        """Возвращает описание задачи."""
        description = f"Эмуляция обработки с паузой {self.processing_time} сек на файл"
        if self.simulate_errors:
            description += (
                f" (с {self.error_probability * 100:.0f}% вероятностью ошибок)"
            )
        return description

    def get_file_pattern(self) -> str:
        """Возвращает паттерн для поиска файлов."""
        return self.file_pattern

    def create_file_task(self, file_path: Path) -> BaseFileTask:
        """Создает тестовую задачу для конкретного файла."""
        task_params = {
            "processing_time": self.processing_time,
            "simulate_errors": self.simulate_errors,
            "error_probability": self.error_probability,
            "skip_large_files": self.skip_large_files,
            "max_file_size": self.max_file_size,
        }
        return TestProcessingTask(file_path, task_params)
