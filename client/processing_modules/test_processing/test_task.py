# processing_modules/test_processing/test_task.py

import asyncio
from pathlib import Path
from typing import Dict, Any, Optional

from loguru import logger

from client.processing_modules.base_classes import BaseFileTask, TaskResult, TaskStatus


class TestProcessingTask(BaseFileTask):
    """Тестовая задача обработки файла с эмуляцией через паузу."""

    def __init__(self, file_path: Path, task_params: Dict[str, Any]):
        super().__init__(file_path, task_params)
        self.processing_time = task_params.get("processing_time", 1.0)  # секунды
        self.simulate_errors = task_params.get("simulate_errors", False)
        self.error_probability = task_params.get(
            "error_probability", 0.1
        )  # 10% вероятность ошибки
        self.skip_large_files = task_params.get("skip_large_files", False)
        self.max_file_size = task_params.get("max_file_size", 1000)  # байты

    def _check_skip_conditions(self) -> Optional[str]:
        """Проверяет условия пропуска файла."""
        # Пример стратегии пропуска: пропускаем большие файлы
        if self.skip_large_files:
            try:
                file_size = self.file_path.stat().st_size
                if file_size > self.max_file_size:
                    return f"файл слишком большой ({file_size} > {self.max_file_size} байт)"
            except OSError:
                return "не удалось получить размер файла"

        return None

    async def process(self) -> TaskResult:
        """Эмулирует обработку файла через паузу."""
        try:
            logger.debug(f"Starting test processing for {self.file_path.name}")

            # Эмулируем обработку через паузу
            await asyncio.sleep(self.processing_time)

            # Эмулируем случайные ошибки, если включено
            if self.simulate_errors:
                import random

                if random.random() < self.error_probability:
                    return TaskResult(
                        status=TaskStatus.ERROR,
                        error=f"Эмулированная ошибка обработки файла {self.file_path.name}",
                    )

            # Успешное завершение
            logger.debug(f"Test processing completed for {self.file_path.name}")
            return TaskResult(
                status=TaskStatus.COMPLETED,
                message=f"Тестовая обработка завершена за {self.processing_time} сек",
                data={
                    "processed_file": str(self.file_path),
                    "processing_time": self.processing_time,
                },
            )

        except Exception as e:
            logger.exception(
                f"Unexpected error during test processing of {self.file_path.name}"
            )
            return TaskResult(
                status=TaskStatus.ERROR, error=f"Неожиданная ошибка: {str(e)}"
            )

    def get_display_name(self) -> str:
        """Возвращает имя файла для отображения."""
        # Возвращаем просто имя файла, обрезка будет происходить в интерфейсе
        return self.file_path.name
