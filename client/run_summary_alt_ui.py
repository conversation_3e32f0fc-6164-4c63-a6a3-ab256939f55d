# run_summary_alt_ui.py

import asyncio
import shutil
import time
from pathlib import Path
from datetime import timedelta

import typer
import httpx
from rich.panel import Panel
from rich.progress import (
    Progress,
    BarColumn,
    TextColumn,
    SpinnerColumn,
)
from rich.console import Console
from loguru import logger

# --- Импорт существующих модулей бэкенда ---
from client.processing_modules.summarization.summarize_modules.config import (
    AppConfig,
    StrategyEnum,
    DEFAULT_HOST,
    DEFAULT_PORT,
    LOG_FILE,
)

from client.processing_modules.summarization.summarize_modules.modes_client import (
    get_modes_for_meta_mode,
)
from client.processing_modules.summarization.summarize_modules.api_client_http import (
    APIClient,
)
from client.processing_modules.summarization.summarize_modules.file_processor import (
    FileProcessor,
)
from client.processing_modules.summarization.summarize_modules.sse_strategy import (
    SSESummarizationStrategy,
)
from client.processing_modules.summarization.summarize_modules.polling_strategy import (
    PollingSummarizationStrategy,
)
from client.processing_modules.summarization.summarize_modules.exceptions import (
    AuthenticationError,
    ServerUnavailableError,
    APITimeoutError,
    TaskFailedError,
    FileProcessingError,
)
from client.processing_modules.summarization.summarize_modules.file_utils import (
    trim_filename,
)

# --- Создание экземпляра Typer ---
app = typer.Typer(help="Инструмент для асинхронной суммаризации с альтернативным UI.")

# --- Вспомогательные функции из скрипта-примера ---
BAR_WIDTH = 40


def format_text_to_width(text: str, width: int) -> str:
    """Форматирует строку до заданной ширины, обрезая или дополняя пробелами."""
    if width <= 0:
        return ""
    # Используем кастомную функцию, чтобы лучше контролировать обрезку с тегами
    # Rich может сам обрезать, но для точного соответствия примеру делаем вручную
    # Убираем теги для подсчета длины
    import re

    plain_text = re.sub(r"\[.*?\]", "", text)
    if len(plain_text) <= width:
        return text.ljust(width + (len(text) - len(plain_text)))

    ellipsis = "..."
    if width < len(ellipsis) + 2:
        return text[:width]

    # chars_to_keep = width - len(ellipsis)
    # start_len = chars_to_keep // 2
    # end_len = chars_to_keep - start_len

    # Эта логика обрезки для простого текста, для rich текста лучше trim_filename
    return trim_filename(text, max_length=width).ljust(width)


def format_time(seconds: float) -> str:
    """Форматирует секунды в строку HH:MM:SS."""
    return str(timedelta(seconds=int(seconds)))


def configure_logging(debug: bool):
    """Настраивает логгер loguru для записи только в файл."""
    logger.remove()
    log_level = "DEBUG" if debug else "INFO"
    logger.add(
        LOG_FILE, level=log_level, rotation="10 MB", compression="zip", enqueue=True
    )
    logger.info("Logging configured. All logs will be written to file.")


@app.command()
def main(
    directory: Path = typer.Argument(
        ...,
        exists=True,
        file_okay=False,
        dir_okay=True,
        help="Директория с файлами для обработки.",
    ),
    mode: str = typer.Option(None, "--mode", "-m", help="Один режим суммаризации."),
    modes: str = typer.Option(None, "--modes", help="Несколько режимов через запятую."),
    meta_mode: str = typer.Option(None, "--meta-mode", help="Мета-режим."),
    host: str = typer.Option(
        DEFAULT_HOST, help=f"Хост сервера (умолч.: {DEFAULT_HOST})."
    ),
    port: int = typer.Option(
        DEFAULT_PORT, "-p", help=f"Порт сервера (умолч.: {DEFAULT_PORT})."
    ),
    format_md: bool = typer.Option(
        False, "--format", "-f", help="Форматировать Markdown."
    ),
    debug: bool = typer.Option(
        False, "--debug", "-d", help="Включить детальное логирование."
    ),
    api_key: str = typer.Option(
        None,
        "--api-key",
        envvar="SUMMARY_API_KEY",
        help="API ключ (или env SUMMARY_API_KEY).",
    ),
    strategy: StrategyEnum = typer.Option(
        StrategyEnum.sse.value,
        "--strategy",
        "-s",
        case_sensitive=False,
        help="Стратегия взаимодействия с API.",
    ),
):
    """Синхронная обёртка для асинхронной логики."""
    try:
        asyncio.run(
            main_async_alt_ui(
                directory,
                mode,
                modes,
                meta_mode,
                host,
                port,
                format_md,
                debug,
                api_key,
                strategy,
            )
        )
    except (typer.Abort, typer.Exit):
        pass  # Тихо выходим
    except Exception as e:
        Console().print(
            f"[bold red]Произошла непредвиденная критическая ошибка. Проверьте лог-файл: {LOG_FILE}[/bold red]"
        )
        logger.exception(f"A critical unhandled exception occurred: {e}")


async def main_async_alt_ui(
    directory: Path,
    mode_arg: str,
    modes_arg: str,
    meta_mode_arg: str,
    host: str,
    port: int,
    format_md: bool,
    debug: bool,
    api_key: str,
    strategy: StrategyEnum,
):
    """Основная асинхронная функция с новым UI."""
    console = Console()
    configure_logging(debug)

    # --- Разбор режимов ---
    if mode_arg:
        modes_to_run = [mode_arg]
    elif modes_arg:
        modes_to_run = [m.strip() for m in modes_arg.split(",")]
    else:
        modes_to_run = get_modes_for_meta_mode(meta_mode_arg)
        if not modes_to_run:
            console.print(
                f"[red]Ошибка: Неизвестный мета-режим '{meta_mode_arg}'[/red]"
            )
            return

    txt_files = sorted(list(directory.glob("**/*.txt")))
    if not txt_files:
        console.print(
            "[yellow]Предупреждение: .txt файлы в директории не найдены.[/yellow]"
        )
        return

    total_operations = len(txt_files) * len(modes_to_run)

    # --- 1. Предварительный отчет и подтверждение ---
    console.print("\n[bold]Обнаружено для обработки:[/bold]")
    console.print(f"• [bold]Папка:[/bold] [cyan]{directory.resolve()}[/cyan]")
    console.print(f"• [bold]Режимы:[/bold] [cyan]{', '.join(modes_to_run)}[/cyan]")
    console.print(f"• [bold]Всего операций:[/bold] [cyan]{total_operations}[/cyan]")

    try:
        typer.confirm("\nНачать обработку?", abort=True, default=True)
    except typer.Abort:
        console.print("[bold red]Отменено пользователем.[/bold red]")
        return

    # --- Инициализация зависимостей ---
    config = AppConfig(host, port, api_key, strategy.value, format_md, debug)
    api_client = APIClient(
        host=config.host, port=config.port, api_key=config.api_key, debug=config.debug
    )
    file_processor = FileProcessor(format_markdown=config.format_md, console=console)
    summarization_strategy = (
        SSESummarizationStrategy
        if config.strategy == "sse"
        else PollingSummarizationStrategy
    )(api_client=api_client, console=console, debug=config.debug)

    try:
        if not await api_client.check_server_available():
            raise ServerUnavailableError(
                f"Сервер недоступен по адресу {api_client.base_url}"
            )
    except (ServerUnavailableError, httpx.RequestError) as e:
        console.print(
            f"\n[bold red]Критическая ошибка связи с сервером: {e}[/bold red]"
        )
        return

    # --- 2. Настройка прогресс-бара ---
    try:
        terminal_width = shutil.get_terminal_size().columns
    except OSError:
        terminal_width = 120
    right_text_width = terminal_width - BAR_WIDTH - 15

    progress_columns = (
        SpinnerColumn(spinner_name="dots"),
        BarColumn(bar_width=BAR_WIDTH),
        TextColumn("{task.fields[right_text]}"),
    )

    overall_stats = {"success": 0, "errors": 0, "skipped": 0, "details": []}
    start_time = time.monotonic()

    with Progress(*progress_columns, console=console) as progress:
        overall_task = progress.add_task(
            "overall", total=total_operations, right_text=""
        )
        file_task = progress.add_task("file", total=100, visible=True, right_text="")

        operation_index = 0
        for current_mode in modes_to_run:
            for file_path in txt_files:
                operation_index += 1
                progress.update(file_task, completed=0, total=100)

                short_name = file_path.name
                display_name = f"📄 ({current_mode}) {short_name}"
                # ОБНОВЛЕНИЕ 1: Текст по умолчанию всегда белый
                formatted_name = format_text_to_width(display_name, right_text_width)
                progress.update(file_task, right_text=f"[white]{formatted_name}[/]")

                try:
                    if file_processor.does_summary_exist(file_path, current_mode):
                        overall_stats["skipped"] += 1
                        # ОБНОВЛЕНИЕ 2: Окрашиваем, делаем паузу
                        status_text = format_text_to_width(
                            f"⏩ Пропущено (есть): {short_name}", right_text_width
                        )
                        progress.update(
                            file_task,
                            completed=100,
                            right_text=f"[bold cyan]{status_text}[/]",
                        )
                        await asyncio.sleep(0.3)
                    else:
                        text_content = file_processor.read_file(file_path)
                        result = await summarization_strategy.process_file(
                            file_path=file_path,
                            mode=current_mode,
                            text_content=text_content,
                            progress=progress,
                            task_id=file_task,
                        )

                        if result["status"] == "completed" and result["summary"]:
                            file_processor.save_summary(
                                result["summary"], file_path, current_mode
                            )
                            overall_stats["success"] += 1
                            status_text = format_text_to_width(
                                f"✅ Готово: {short_name}", right_text_width
                            )
                            progress.update(
                                file_task, right_text=f"[bold green]{status_text}[/]"
                            )
                            await asyncio.sleep(0.5)
                        else:
                            error_msg = result.get("error", "Неизвестная ошибка")
                            overall_stats["errors"] += 1
                            overall_stats["details"].append(
                                (f"{short_name} ({current_mode})", error_msg)
                            )
                            status_text = format_text_to_width(
                                f"❌ Ошибка: {short_name}", right_text_width
                            )
                            progress.update(
                                file_task, right_text=f"[bold red]{status_text}[/]"
                            )
                            await asyncio.sleep(1.5)

                except (
                    AuthenticationError,
                    APITimeoutError,
                    TaskFailedError,
                    FileProcessingError,
                    httpx.RequestError,
                    ValueError,
                ) as e:
                    overall_stats["errors"] += 1
                    overall_stats["details"].append(
                        (f"{short_name} ({current_mode})", str(e))
                    )
                    status_text = format_text_to_width(
                        f"❌ Ошибка: {short_name}", right_text_width
                    )
                    progress.update(
                        file_task,
                        completed=100,
                        right_text=f"[bold red]{status_text}[/]",
                    )
                    await asyncio.sleep(1.5)

                finally:
                    progress.update(overall_task, advance=1)
                    overall_data = progress.tasks[overall_task]
                    elapsed = overall_data.elapsed or 0
                    speed = overall_data.speed or 0

                    pace_str = f"{1 / speed:.2f} с/оп" if speed > 0 else "--- с/оп"
                    remaining_str = (
                        format_time(overall_data.time_remaining)
                        if overall_data.time_remaining is not None
                        else "---"
                    )

                    stats_text = (
                        f"[cyan]({operation_index}/{total_operations})[/] | ⏱️  [yellow]{format_time(elapsed)}[/] "
                        f"| ⏳ ~[yellow]{remaining_str}[/] | ⚙️  [magenta]{pace_str}[/]"
                    )
                    progress.update(overall_task, right_text=stats_text)

        # ОБНОВЛЕНИЕ 3: Скрываем таск файла в самом конце цикла
        progress.update(file_task, visible=False)
        final_text = "Подготовка отчета..."
        progress.update(overall_task, right_text=f"[bold blue]{final_text}[/]")
        await asyncio.sleep(0.5)

    # --- 3. Детальный итоговый отчет ---
    total_time_secs = time.monotonic() - start_time
    total_time_str = format_time(total_time_secs)

    labels = ["Успешно:", "С ошибками:", "Пропущено:", "Всего операций:", "Затрачено:"]
    values = [
        str(s)
        for s in [
            overall_stats["success"],
            overall_stats["errors"],
            overall_stats["skipped"],
            total_operations,
        ]
    ] + [total_time_str]

    max_label = max(len(label) for label in labels)
    max_val = max(len(value) for value in values)

    report_lines = [
        "[underline]Итоговый отчет:[/underline]",
        f"  • {labels[0].ljust(max_label)} [green]{values[0]:>{max_val}}[/green]",
        f"  • {labels[1].ljust(max_label)} [red]{values[1]:>{max_val}}[/red]",
        f"  • {labels[2].ljust(max_label)} [cyan]{values[2]:>{max_val}}[/cyan]",
        f"  • {labels[3].ljust(max_label)} {values[3]:>{max_val}}",
        f"  • {labels[4].ljust(max_label)} [yellow]{values[4]:>{max_val}}[/yellow]",
    ]

    if overall_stats["details"]:
        report_lines.append("\n[bold red]Список операций с ошибками:[/bold red]")
        for i, (name, err) in enumerate(overall_stats["details"], 1):
            report_lines.append(f"  {i}. {name} - [italic]{err}[/italic]")

    report = "\n".join(report_lines)
    console.print(
        Panel(
            report,
            title="[bold green]✅ Обработка завершена[/bold green]",
            border_style="blue",
        )
    )


if __name__ == "__main__":
    app()
