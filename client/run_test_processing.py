# run_test_processing.py

import asyncio
from pathlib import Path

import typer
from loguru import logger
from rich.console import Console

from client.processing_modules.test_processing.config import LOG_FILE
from client.processing_modules.test_processing.test_app import TestProcessingApp

app = typer.Typer(
    help="Инструмент для тестовой обработки файлов с эмуляцией через паузу."
)


def configure_logging(debug: bool):
    """Настраивает логгер loguru для записи только в файл."""
    logger.remove()
    log_level = "DEBUG" if debug else "INFO"
    logger.add(
        LOG_FILE, level=log_level, rotation="10 MB", compression="zip", enqueue=True
    )
    logger.info("Logging configured. All logs will be written to file.")


@app.command()
def main(
    directory: Path = typer.Argument(
        ...,
        exists=True,
        file_okay=False,
        dir_okay=True,
        help="Директория с файлами для обработки.",
    ),
    processing_time: float = typer.Option(
        1.0, "--time", "-t", help="Время обработки одного файла в секундах."
    ),
    file_pattern: str = typer.Option(
        "**/*.txt", "--pattern", "-p", help="Паттерн для поиска файлов."
    ),
    simulate_errors: bool = typer.Option(
        False, "--errors", "-e", help="Эмулировать случайные ошибки."
    ),
    error_probability: float = typer.Option(
        0.1, "--error-rate", help="Вероятность ошибки (0.0-1.0)."
    ),
    skip_large_files: bool = typer.Option(
        False, "--skip-large", help="Пропускать большие файлы."
    ),
    max_file_size: int = typer.Option(
        100, "--max-size", help="Максимальный размер файла в байтах для пропуска."
    ),
    auto_start: bool = typer.Option(
        False, "--auto", help="Автоматически начать без подтверждения."
    ),
    debug: bool = typer.Option(
        False, "--debug", "-d", help="Включить детальное логирование."
    ),
):
    """Синхронная обёртка для асинхронной логики тестовой обработки."""
    try:
        asyncio.run(
            main_async(
                directory,
                processing_time,
                file_pattern,
                simulate_errors,
                error_probability,
                skip_large_files,
                max_file_size,
                auto_start,
                debug,
            )
        )
    except (typer.Abort, typer.Exit):
        pass  # Тихо выходим
    except Exception as e:
        Console().print(
            f"[bold red]Произошла непредвиденная критическая ошибка. Проверьте лог-файл: {LOG_FILE}[/bold red]"
        )
        logger.exception(f"A critical unhandled exception occurred: {e}")


async def main_async(
    directory: Path,
    processing_time: float,
    file_pattern: str,
    simulate_errors: bool,
    error_probability: float,
    skip_large_files: bool,
    max_file_size: int,
    auto_start: bool,
    debug: bool,
):
    """Настраивает и запускает приложение тестовой обработки."""
    configure_logging(debug)

    # Валидация параметров
    if processing_time <= 0:
        Console().print("[red]Ошибка: Время обработки должно быть больше 0[/red]")
        raise typer.Exit(1)

    if not 0.0 <= error_probability <= 1.0:
        Console().print(
            "[red]Ошибка: Вероятность ошибки должна быть между 0.0 и 1.0[/red]"
        )
        raise typer.Exit(1)

    app_instance = TestProcessingApp(
        directory=directory,
        processing_time=processing_time,
        file_pattern=file_pattern,
        simulate_errors=simulate_errors,
        error_probability=error_probability,
        skip_large_files=skip_large_files,
        max_file_size=max_file_size,
        auto_start=auto_start,
    )
    await app_instance.run()


if __name__ == "__main__":
    app()
