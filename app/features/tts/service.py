from typing import Optional
from sqlmodel import Session, select
from sqlalchemy.exc import IntegrityError
from app.features.tts.models import TTS
from app.core.logging_config import logger
from arq.connections import ArqRedis
import xxhash


class TTSService:
    def __init__(self, db_session: Session, arq_redis: ArqRedis):
        self.db_session = db_session
        self.arq_redis = arq_redis

    def generate_text_xxhash(self, text: str) -> str:
        """Generates a XXH3 hash for the given text."""
        return xxhash.xxh3_64_hexdigest(text.strip().encode("utf-8"))

    async def submit_tts_task(self, text: str, engine: str) -> str:
        text_hash = self.generate_text_xxhash(text)
        logger.info(
            "Отправка задачи TTS",
            text_hash=text_hash,
            text_length=len(text),
            engine=engine,
        )

        # Check if TTS entry already exists for this text_hash and engine
        existing_tts_entry = (
            await self.db_session.execute(
                select(TTS).where(TTS.text_hash == text_hash, TTS.engine == engine)
            )
        ).scalar_one_or_none()
        if existing_tts_entry:
            logger.info(
                "Запись TTS уже существует для этого хеша текста и движка. Возвращается существующий text_hash.",
                text_hash=text_hash,
                engine=engine,
            )
            # Return the composite ID for consistency, though the API will rebuild it
            return f"{text_hash}_tts_{engine}"

        # Create a new TTS entry in the database
        new_tts_entry = TTS(
            text=text,
            engine=engine,
            text_hash=text_hash,
        )
        self.db_session.add(new_tts_entry)
        await self.db_session.commit()
        await self.db_session.refresh(new_tts_entry)

        # Enqueue the ARQ task
        await self.arq_redis.enqueue_job(
            "synthesize_audio",  # This will be the ARQ worker function name
            text=text,
            engine=engine,  # Pass engine directly as a string
            _job_timeout=900,  # Устанавливаем таймаут для этой задачи
            _job_id=f"{text_hash}_tts_{engine}",  # Explicitly set the composite job_id
        )
        logger.info(
            "Задача TTS успешно поставлена в очередь",
            text_hash=text_hash,
            engine=engine,
        )
        return f"{text_hash}_tts_{engine}"

    async def get_tts_status(self, text_hash: str, engine: str) -> Optional[TTS]:
        logger.info("Получение статуса TTS", text_hash=text_hash, engine=engine)
        tts_entry = (
            await self.db_session.execute(
                select(TTS).where(TTS.text_hash == text_hash, TTS.engine == engine)
            )
        ).scalar_one_or_none()
        if tts_entry:
            logger.info(
                "Запись TTS найдена",
                text_hash=text_hash,
                engine=engine,
                status=tts_entry.file_path is not None,
            )
        else:
            logger.warning("Запись TTS не найдена", text_hash=text_hash, engine=engine)
        return tts_entry

    async def update_tts_status(
        self,
        text_hash: str,
        engine: str,
        file_path: str,
        file_size_bytes: int,
        text: str,
        file_name: str,
        s3_url: Optional[str] = None,
    ) -> None:
        logger.info(
            "Обновление статуса TTS",
            text_hash=text_hash,
            engine=engine,
            file_path=file_path,
            file_size_bytes=file_size_bytes,
        )

        # Попытка найти существующую запись
        tts_entry = (
            await self.db_session.execute(
                select(TTS).where(TTS.text_hash == text_hash, TTS.engine == engine)
            )
        ).scalar_one_or_none()

        if tts_entry:
            # Обновляем существующую запись
            tts_entry.file_path = file_path
            tts_entry.file_name = file_name
            tts_entry.file_size_bytes = file_size_bytes
            if s3_url:
                tts_entry.s3_url = s3_url
            self.db_session.add(tts_entry)
            logger.info(
                "Запись TTS успешно обновлена", text_hash=text_hash, engine=engine
            )
        else:
            # Создаем новую запись с обработкой race condition
            logger.info("Запись TTS не найдена, создается новая.")
            tts_entry = TTS(
                text_hash=text_hash,
                engine=engine,
                text=text,
                file_path=file_path,
                file_name=file_name,
                file_size_bytes=file_size_bytes,
                s3_url=s3_url,
            )
            self.db_session.add(tts_entry)

        try:
            await self.db_session.commit()
            await self.db_session.refresh(tts_entry)
        except IntegrityError:
            # Race condition: другой воркер уже создал запись
            logger.warning(
                f"Обнаружена гонка условий для text_hash={text_hash}, engine={engine}. "
                "Другой воркер уже создал запись. Повторная попытка обновления..."
            )
            await self.db_session.rollback()

            # Повторно получаем запись и обновляем её
            tts_entry = (
                await self.db_session.execute(
                    select(TTS).where(TTS.text_hash == text_hash, TTS.engine == engine)
                )
            ).scalar_one_or_none()

            if tts_entry:
                tts_entry.file_path = file_path
                tts_entry.file_name = file_name
                tts_entry.file_size_bytes = file_size_bytes
                if s3_url:
                    tts_entry.s3_url = s3_url
                await self.db_session.commit()
                await self.db_session.refresh(tts_entry)
                logger.info(
                    "Запись TTS успешно обновлена после гонки условий",
                    text_hash=text_hash,
                    engine=engine,
                )
            else:
                logger.error(
                    f"Не удалось найти запись TTS после гонки условий для "
                    f"text_hash={text_hash}, engine={engine}"
                )
                raise
