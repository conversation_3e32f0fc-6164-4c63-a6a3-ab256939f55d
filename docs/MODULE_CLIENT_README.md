# Рефакторинг системы обработки файлов

## Обзор

Система была рефакторирована для создания универсального интерфейса обработки файлов с использованием ООП подхода. Теперь суммаризация является одной из реализаций общей архитектуры обработки файлов.

## Архитектура

### Базовые классы (`client/processing_modules/base_classes.py`)

- **`TaskStatus`** - Enum для статусов задач (pending, processing, completed, error, skipped)
- **`TaskResult`** - Dataclass для результатов выполнения задач
- **`BaseFileTask`** - Абстрактный класс для задач обработки отдельных файлов
- **`BaseTaskOrchestrator`** - Абстрактный класс-оркестратор для управления множественными задачами

### Универсальный интерфейс (`client/universal_interface.py`)

- **`UniversalInterface`** - Класс для отображения процесса обработки файлов
- Поддерживает отображение заголовка, плана обработки, прогресса и итогового отчета
- Использует существующий внешний вид из оригинальной системы

### Модули обработки (`client/processing_modules/`)

#### Суммаризация (`client/processing_modules/summarization/`)
- **`SummarizationTask`** - Задача суммаризации отдельного файла
- **`SummarizationOrchestrator`** - Оркестратор для суммаризации множественных файлов
- **`SummarizationApp`** - Главное приложение суммаризации

#### Тестовая обработка (`client/processing_modules/test_processing/`)
- **`TestProcessingTask`** - Тестовая задача с эмуляцией через паузу
- **`TestProcessingOrchestrator`** - Оркестратор для тестовой обработки
- **`TestProcessingApp`** - Приложение тестовой обработки

## Использование

### Суммаризация (как раньше)
```bash
# Один режим
uv run python client/run_summary.py test_files --mode default

# Несколько режимов
uv run python client/run_summary.py test_files --modes "mode1,mode2,mode3"

# Мета-режим
uv run python client/run_summary.py test_files --meta-mode all
```

### Тестовая обработка
```bash
# Базовая тестовая обработка
uv run python client/run_test_processing.py test_files

# С настройками времени и ошибок
uv run python client/run_test_processing.py test_files --time 0.5 --errors --error-rate 0.3

# Другой паттерн файлов
uv run python client/run_test_processing.py test_files --pattern "**/*.md"
```

### Демонстрационный скрипт
```bash
# Тестовая обработка
uv run python client/run_demo.py test test_files --time 0.5

# Информация о суммаризации
uv run python client/run_demo.py summarize test_files --mode default
```

## Создание новых типов обработки

### 1. Создайте задачу файла
```python
from client.processing_modules.base_classes import BaseFileTask, TaskResult, TaskStatus

class MyProcessingTask(BaseFileTask):
    def should_skip(self) -> bool:
        # Логика пропуска файла
        return False
    
    async def process(self) -> TaskResult:
        # Логика обработки файла
        return TaskResult(status=TaskStatus.COMPLETED)
```

### 2. Создайте оркестратор
```python
from client.processing_modules.base_classes import BaseTaskOrchestrator

class MyProcessingOrchestrator(BaseTaskOrchestrator):
    def get_task_name(self) -> str:
        return "Моя обработка файлов"
    
    def get_task_description(self) -> str:
        return "Описание обработки"
    
    def get_file_pattern(self) -> str:
        return "**/*.txt"
    
    def create_file_task(self, file_path: Path) -> BaseFileTask:
        return MyProcessingTask(file_path, self.task_params)
```

### 3. Создайте приложение
```python
from client.processing_modules.universal_interface import UniversalInterface

class MyProcessingApp:
    def __init__(self, directory: Path, **kwargs):
        self.console = Console()
        self.interface = UniversalInterface(self.console)
        self.orchestrator = MyProcessingOrchestrator(directory, **kwargs)
    
    async def run(self):
        # Используйте self.interface для отображения
        # Используйте self.orchestrator для обработки
```

## Особенности

### Внешний вид
- Сохранен оригинальный внешний вид интерфейса
- Заголовок с названием типа обработки
- План обработки с деталями
- Общий прогресс и последние 10 файлов
- Итоговый отчет с статистикой

### Статусы задач
- **Pending** - Ожидание обработки
- **Processing** - В процессе обработки  
- **Completed** - Успешно завершено
- **Error** - Ошибка при обработке
- **Skipped** - Пропущено

### Обработка ошибок
- Каждая задача обрабатывает свои ошибки
- Оркестратор собирает статистику ошибок
- Интерфейс отображает детали ошибок в итоговом отчете

## Тестирование

Создана тестовая директория `test_files/` с тремя файлами для демонстрации работы системы.

### Тестовые команды
```bash
# Быстрая тестовая обработка
uv run python client/run_test_processing.py test_files --time 0.3

# С эмуляцией ошибок
uv run python client/run_test_processing.py test_files --time 0.5 --errors --error-rate 0.5

# Суммаризация (требует сервер)
uv run python client/run_summary.py test_files --mode default
```
