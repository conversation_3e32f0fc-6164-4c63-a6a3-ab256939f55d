// Функция для асинхронного скачивания субтитров через REST API
async function fetchSubtitlesViaAPI(youtubeUrl) {
  const baseUrl = "http://localhost:8001";
  try {
    // 1. POST /subtitles
    const resp = await fetch(`${baseUrl}/subtitles`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ url: youtubeUrl }),
    });
    if (!resp.ok) throw new Error("Ошибка запуска задачи на сервере");
    const respData = await resp.json();
    const { task_id, status_url, result_url } = respData;
    if (!status_url && !result_url)
      throw new Error("Некорректный ответ сервера");

    // Если есть result_url — сразу скачиваем результат
    if (result_url) {
      const resultResp = await fetch(result_url);
      if (!resultResp.ok) throw new Error("Ошибка получения результата");
      const resultData = await resultResp.json();
      if (resultData.status !== "completed")
        throw new Error("Задача не завершена");
      // Map upload_date to publish_date for consistency with content.js
      if (resultData.result && resultData.result.upload_date) {
        resultData.result.publish_date = resultData.result.upload_date;
      }
      return { ok: true, result: resultData.result };
    }

    // Poll status_url
    let status = null,
      poll_result_url = null,
      error = null;
    while (true) {
      const statusResp = await fetch(status_url);
      if (!statusResp.ok) throw new Error("Ошибка получения статуса задачи");
      const statusData = await statusResp.json();
      status = statusData.status;
      poll_result_url = statusData.result_url;
      error = statusData.error;
      if (status === "completed") break;
      if (error) throw new Error(error);
      await new Promise((res) => setTimeout(res, 2000));
    }

    if (!poll_result_url)
      throw new Error("Сервер не вернул ссылку на результат");
    const resultResp = await fetch(poll_result_url);
    if (!resultResp.ok) throw new Error("Ошибка получения результата");
    const resultData = await resultResp.json();
    if (resultData.status !== "completed")
      throw new Error("Задача не завершена");
    // Map upload_date to publish_date for consistency with content.js
    if (resultData.result && resultData.result.upload_date) {
      resultData.result.publish_date = resultData.result.upload_date;
    }
    return { ok: true, result: resultData.result };
  } catch (e) {
    return { ok: false, error: e.message || "Неизвестная ошибка" };
  }
}

// Слушаем сообщения от content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request && request.type === "FETCH_SUBTITLES" && request.youtubeUrl) {
    fetchSubtitlesViaAPI(request.youtubeUrl).then((result) => {
      sendResponse(result);
    });
    return true; // Ожидаем асинхронный ответ
  }
});
