<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>YouTube Subtitles Downloader</title>
    <style>
      body {
        width: 300px;
        padding: 15px;
        font-family: system-ui, -apple-system, sans-serif;
      }
      .status {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
        display: none;
      }
      .status.info {
        display: block;
        background: #e3f2fd;
        color: #1976d2;
      }
      .status.error {
        display: block;
        background: #ffebee;
        color: #d32f2f;
      }
      .status.success {
        display: block;
        background: #e8f5e9;
        color: #388e3c;
      }
      .language-info {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
        background: #f5f5f5;
        color: #333;
        font-size: 14px;
        display: none;
      }
      .language-info.visible {
        display: block;
      }
      button {
        width: 100%;
        padding: 10px;
        background: #1976d2;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      button:hover:not(:disabled) {
        background: #1565c0;
      }
    </style>
  </head>
  <body>
    <div id="status" class="status"></div>
    <div id="language-info" class="language-info"></div>
    <button id="downloadBtn">Download Subtitles</button>

    <script src="js/api.js"></script>
    <script src="js/downloader.js"></script>
    <script src="popup.js"></script>
  </body>
</html>
