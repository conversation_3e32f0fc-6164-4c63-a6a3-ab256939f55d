// Helper to set button final state
function setButtonFinalState(button, success) {
  button.disabled = true; // Always disable after operation
  button.dispatchEvent(new Event("disabled-changed")); // Update visual disabled state
  if (success) {
    button.style.backgroundColor = "rgba(0, 200, 50, 0.3)"; // Slightly greenish with transparency
  } else {
    button.style.backgroundColor = "rgba(200, 50, 0, 0.3)"; // Slightly reddish with transparency
  }
}

// Функция для создания безопасного имени файла (во встроенной кнопке)
function createSafeFileName(title, date) {
  // Регулярное выражение для удаления недопустимых символов
  const sanitized = title
    .trim()
    .replace(/[\/:\"*?<>|]+/g, "_") // Удаляем недопустимые символы
    .replace(/\s+/g, " ") // Заменяем пробелы
    .replace(/_+/g, "_") // Заменяем множественные подчеркивания
    .replace(/^_+|_+$/g, ""); // Удаляем начальные/конечные подчеркивания
  const suffix = date ? `_${date.split("T")[0].replace(/-/g, "")}` : "";
  return `[Local] ${sanitized}${suffix} [DownSub-Alt]`;
}

// Функция для создания кнопки CC в стиле YouTube
function createCCButton() {
  const button = document.createElement("button");
  button.className =
    "yt-spec-button-shape-next yt-spec-button-shape-next--tonal yt-spec-button-shape-next--mono yt-spec-button-shape-next--size-m yt-spec-button-shape-next--icon-leading yt-spec-button-shape-next--enable-backdrop-filter-experiment";
  button.title = "Скачать субтитры";
  button.style.marginRight = "8px";

  // Стили для отключенного состояния
  button.addEventListener("disabled-changed", () => {
    if (button.disabled) {
      button.style.opacity = "0.5";
      button.style.cursor = "not-allowed";
    } else {
      button.style.opacity = "1";
      button.style.cursor = "pointer";
    }
  });

  // Создаем текстовое содержимое кнопки
  const textContent = document.createElement("div");
  textContent.className = "yt-spec-button-shape-next__button-text-content";
  textContent.textContent = "CC";

  button.appendChild(textContent);

  // Добавляем эффект при нажатии
  const touchFeedback = document.createElement("yt-touch-feedback-shape");
  touchFeedback.style.borderRadius = "inherit";
  const touchDiv = document.createElement("div");
  touchDiv.className =
    "yt-spec-touch-feedback-shape yt-spec-touch-feedback-shape--touch-response";
  touchDiv.setAttribute("aria-hidden", "true");

  const strokeDiv = document.createElement("div");
  strokeDiv.className = "yt-spec-touch-feedback-shape__stroke";
  const fillDiv = document.createElement("div");
  fillDiv.className = "yt-spec-touch-feedback-shape__fill";

  touchDiv.appendChild(strokeDiv);
  touchDiv.appendChild(fillDiv);
  touchFeedback.appendChild(touchDiv);
  button.appendChild(touchFeedback);

  // Добавляем обработчик события
  button.addEventListener("click", handleCCButtonClick);

  return button;
}

// Функция для поиска контейнера с кнопками управления
function findTopLevelButtons() {
  // Возможные селекторы контейнера с кнопками (YouTube может менять их со временем)
  const possibleSelectors = [
    "#top-level-buttons-computed",
    '[id^="top-level-buttons"]',
    "#top-level-buttons",
    ".ytd-video-primary-info-renderer",
    "#top-level-buttons-computed.ytd-menu-renderer",
    ".ytd-video-primary-info-renderer > div:first-child",
    "#menu-container + #button-shape",
  ];

  // Пробуем найти контейнер по каждому селектору
  for (const selector of possibleSelectors) {
    const element = document.querySelector(selector);
    if (element) {
      console.log("Найден контейнер кнопок по селектору:", selector);
      return element;
    }
  }
  console.warn("Не удалось найти контейнер кнопок на странице");
  return null;
}

// Функция для вставки кнопки CC в интерфейс
function insertCCButton() {
  const container = findTopLevelButtons();
  if (!container) {
    console.warn("Контейнер для вставки кнопки не найден");
    return false;
  }

  // Проверяем, не добавлена ли уже наша кнопка
  const existingButton = container.querySelector(
    'button[title="Скачать субтитры"]'
  );
  if (existingButton) {
    console.log("Кнопка уже существует, пропускаем вставку");
    return true;
  }

  try {
    const ccButton = createCCButton();
    if (!ccButton) {
      console.error("Не удалось создать кнопку");
      return false;
    }

    // Пытаемся вставить кнопку в начало контейнера
    container.insertBefore(ccButton, container.firstChild);
    console.log("Кнопка успешно добавлена");
    return true;
  } catch (error) {
    console.error("Ошибка при вставке кнопки:", error);
    return false;
  }
}

// Функция для попытки вставки кнопки с таймаутом
function tryInsertWithTimeout(attempt = 1, maxAttempts = 5, delay = 500) {
  console.log(`Попытка вставки кнопки (${attempt}/${maxAttempts})...`);

  if (attempt > maxAttempts) {
    console.warn("Достигнуто максимальное количество попыток вставки кнопки");
    return;
  }

  if (insertCCButton()) {
    console.log("Кнопка успешно вставлена с попытки", attempt);
  } else {
    console.log(`Повторная попытка через ${delay} мс...`);
    setTimeout(
      () => tryInsertWithTimeout(attempt + 1, maxAttempts, delay * 1.5),
      delay
    );
  }
}

// Наблюдатель за изменениями в DOM для добавления кнопки при загрузке страницы
const observer = new MutationObserver((mutations) => {
  for (const mutation of mutations) {
    if (mutation.addedNodes.length) {
      // Пробуем вставить кнопку при любом изменении DOM
      // Функция сама проверит, нужно ли вставлять кнопку
      tryInsertWithTimeout(1, 3, 300);
      break;
    }
  }
});

// Запускаем наблюдатель
observer.observe(document.body, {
  childList: true,
  subtree: true,
});

// Пробуем вставить кнопку сразу при загрузке скрипта
console.log("Инициализация расширения...");
tryInsertWithTimeout(1, 5, 500);

// Расширенная версия функции для обработки нажатия на кнопку CC
function handleCCButtonClick(event) {
  // Предотвращаем всплытие события
  event.preventDefault();
  event.stopPropagation();

  console.log("CC кнопка была нажата!");

  // Получаем кнопку и URL текущего видео
  const button = event.target.closest("button");
  const videoUrl = document.location.href;

  // Отключаем кнопку перед запросом
  button.disabled = true;

  // Отправляем запрос на скачивание субтитров через background.js
  chrome.runtime.sendMessage(
    {
      type: "FETCH_SUBTITLES",
      youtubeUrl: videoUrl,
    },
    async (response) => {
      if (response && response.ok) {
        await handleComplete(response.result);
      } else {
        handleError(
          response && response.error ? response.error : "Неизвестная ошибка"
        );
      }
    }
  );
  button.dispatchEvent(new Event("disabled-changed"));

  // Обработка статуса загрузки
  const handleStatusUpdate = (status) => {
    if (status === "processing") {
      console.log("Загрузка субтитров...");
      button.disabled = true;
      button.dispatchEvent(new Event("disabled-changed"));
    }
  };

  // Обработка ошибок
  const handleError = (error) => {
    console.error("Ошибка:", error);
    setButtonFinalState(button, false);
  };

  // Обработка успешного получения субтитров
  const handleComplete = async (data) => {
    let downloadAttempted = false; // Flag to track if download was attempted
    let downloadSuccessful = false; // Flag to track if download was successful

    try {
      console.log("wsService.onComplete", data);
      console.log("Content script received data:", data);

      // Функция для скачивания файла через программный клик по ссылке
      const downloadFile = (content, filename) => {
        const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        link.style.display = "none";
        document.body.appendChild(link);

        link.click();

        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      };

      // Загружаем русские субтитры, если оригинальный язык русский
      if (data.original_language === "ru" && data.ru_subtitles) {
        try {
          downloadAttempted = true;
          const filename = `${createSafeFileName(
            data.title,
            data.publish_date
          )}.txt`;
          downloadFile(data.ru_subtitles, filename);
          console.log(
            "Content: русские субтитры успешно отправлены на скачивание"
          );
          downloadSuccessful = true;
        } catch (error) {
          console.error(
            "Content: ошибка при скачивании русских субтитров",
            error
          );
        }
      }
      // Загружаем английские субтитры для всех остальных языков
      else if (data.en_subtitles) {
        try {
          downloadAttempted = true;
          const filename = `${createSafeFileName(
            data.title,
            data.publish_date
          )}.txt`;
          downloadFile(data.en_subtitles, filename);
          console.log("Content: english subtitles sent for download");
          downloadSuccessful = true;
        } catch (error) {
          console.error("Content: error downloading english subtitles", error);
        }
      }
      // Загружаем русские субтитры если оригинальный язык не русский и если en_subtitles пустые
      else if (data.ru_subtitles) {
        try {
          // Content кнопка загрузка английских субтитров
          downloadAttempted = true;
          const filename = `${createSafeFileName(
            data.title,
            data.publish_date
          )}.txt`;
          downloadFile(data.ru_subtitles, filename);
          console.log("Content: reset to russian subtitles sent for download");
          downloadSuccessful = true;
        } catch (error) {
          console.error("Content: error downloading russian subtitles", error);
        }
      }
      // Субтитры не найдены
      else {
        const error = new Error("Субтитры не найдены для этого видео");
        console.error("Content:", error);
      }
    } catch (error) {
      console.error("Не удалось сохранить файл субтитров", error);
    } finally {
      setButtonFinalState(button, downloadAttempted && downloadSuccessful);
    }
  };

  // Запрашиваем субтитры
  apiService
    .requestSubtitles(videoUrl, {
      onStatusUpdate: handleStatusUpdate,
      onComplete: handleComplete,
      onError: handleError,
    })
    .catch((error) => {
      console.error("Ошибка подключения к серверу", error);
      button.disabled = false;
      button.dispatchEvent(new Event("disabled-changed"));
    });
}
