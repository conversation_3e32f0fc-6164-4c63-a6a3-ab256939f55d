class SubtitlesDownloader {
  static isYouTubeVideoPage(url) {
    try {
      const urlObj = new URL(url);
      return (
        urlObj.hostname === "www.youtube.com" &&
        urlObj.pathname.includes("/watch") &&
        urlObj.searchParams.has("v")
      );
    } catch (error) {
      return false;
    }
  }

  static getCurrentVideoUrl(url) {
    return url;
  }

  static sanitizeTitle(title) {
    return title
      .trim()
      .replace(/[\/:\"*?<>|]+/g, "_") // Remove characters invalid for filenames
      .replace(/\s+/g, " ") // Replace spaces with underscores
      .replace(/_+/g, "_") // Replace multiple underscores with single one
      .replace(/^_+|_+$/g, ""); // Remove leading/trailing underscores
  }

  static async downloadSubtitles(title, subtitles, date, lang) {
    if (!subtitles) return;

    // Create a Blob with the subtitles content
    const blob = new Blob([subtitles], { type: "text/plain;charset=utf-8" });

    // Create the download URL
    const url = URL.createObjectURL(blob);

    const sanitizedTitle = this.sanitizeTitle(title);
    // Popup кнопка загрузки
    const suffix = date ? `_${date.split("T")[0].replace(/-/g, "")}` : "";
    const filename = `[Local] ${sanitizedTitle}${suffix} [DownSub-Alt].txt`;

    try {
      // Use Chrome's download API
      await chrome.downloads.download({
        url: url,
        filename: filename,
        saveAs: false,
      });
    } catch (error) {
      console.error("Download error:", error);
      throw error;
    } finally {
      // Clean up the URL
      URL.revokeObjectURL(url);
    }
  }
}
