// Новый REST-клиент для асинхронного скачивания субтитров
class SubtitlesAPI {
  constructor(baseUrl = "http://localhost:8001") {
    this.baseUrl = baseUrl;
    this.pollInterval = 2000; // мс
  }

  async requestSubtitles(youtubeUrl, { onStatusUpdate, onError, onComplete }) {
    try {
      // 1. POST /subtitles/ для запуска задачи
      const resp = await fetch(`${this.baseUrl}/subtitles`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url: youtubeUrl }),
      });
      if (!resp.ok) throw new Error("Ошибка запуска задачи на сервере");
      const respData = await resp.json();
      const { task_id, status_url, result_url } = respData;
      if (!status_url && !result_url)
        throw new Error("Некорректный ответ сервера");
      if (onStatusUpdate) onStatusUpdate("processing");

      // Если сервер сразу вернул result_url (например, из кеша) — сразу скачиваем результат
      if (result_url) {
        const resultResp = await fetch(result_url);
        if (!resultResp.ok) throw new Error("Ошибка получения результата");
        const resultData = await resultResp.json();
        if (resultData.status !== "completed")
          throw new Error("Задача не завершена");
        // Map upload_date to publish_date for consistency with downloader
        if (resultData.result && resultData.result.upload_date) {
          resultData.result.publish_date = resultData.result.upload_date;
        }
        if (onComplete) onComplete(resultData.result);
        return;
      }

      // 2. Poll status_url
      let status = null,
        poll_result_url = null,
        error = null;
      while (true) {
        const statusResp = await fetch(status_url);
        if (!statusResp.ok) throw new Error("Ошибка получения статуса задачи");
        const statusData = await statusResp.json();
        status = statusData.status;
        poll_result_url = statusData.result_url;
        error = statusData.error;
        if (status === "completed") break;
        if (error) throw new Error(error);
        if (onStatusUpdate) onStatusUpdate(status);
        await new Promise((res) => setTimeout(res, this.pollInterval));
      }

      // 3. GET result_url (после polling)
      if (!poll_result_url)
        throw new Error("Сервер не вернул ссылку на результат");
      const resultResp = await fetch(poll_result_url);
      if (!resultResp.ok) throw new Error("Ошибка получения результата");
      const resultData = await resultResp.json();
      if (resultData.status !== "completed")
        throw new Error("Задача не завершена");
      // Map upload_date to publish_date for consistency with downloader
      if (resultData.result && resultData.result.upload_date) {
        resultData.result.publish_date = resultData.result.upload_date;
      }
      if (onComplete) onComplete(resultData.result);
    } catch (err) {
      if (onError) onError(err.message || "Неизвестная ошибка");
    }
  }
}

// Экспортируем для использования в popup.js и content.js
window.SubtitlesAPI = SubtitlesAPI;
