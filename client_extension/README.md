# 🎬 Загрузчик субтитров с YouTube

Это простое расширение для браузеров, которое позволяет скачивать субтитры с видеороликов на YouTube, используя промежуточный API сервер.

Проект содержит версии для двух популярных браузеров:

*   **Chrome:** В папке `client_extension/chrome`.
*   **Firefox:** В папке `client_extension/firefox`.

## Установка

Чтобы установить расширение из исходного кода, выполните следующие шаги:

### Для Google Chrome

1.  Откройте браузер Chrome.
2.  Перейдите по адресу `chrome://extensions`.
3.  Включите "Режим разработчика" (обычно переключатель в правом верхнем углу).
4.  Нажмите кнопку "Загрузить распакованное расширение".
5.  Выберите папку `client_extension/chrome` из проекта.

Расширение появится в списке установленных.

### Для Mozilla Firefox

1.  Откройте браузер Firefox.
2.  Перейдите по адресу `about:debugging#/runtime/this-firefox`.
3.  Нажмите кнопку "Загрузить временное дополнение...".
4.  В диалоговом окне выберите файл `manifest.json` внутри папки `client_extension/firefox`.

Расширение будет временно установлено до закрытия браузера. Для постоянной установки требуются дополнительные шаги (например, подписание дополнения), которые не рассматриваются в этом README.

## Основные точки входа и файлы

Проект расширения состоит из нескольких ключевых файлов:

*   `manifest.json`: Главный файл конфигурации расширения. Определяет его название, версию, необходимые разрешения и какие скрипты запускать.
    ```json
    {
      "manifest_version": 3, // Версия манифеста (может отличаться для Chrome и Firefox)
      "name": "YouTube Subtitles Downloader",
      "version": "1.0",
      "description": "Download YouTube video subtitles in Russian and English",
      "permissions": ["activeTab", "downloads"], // Необходимые разрешения
      "action": { // Определяет кнопку расширения в Chrome (для Firefox используется "browser_action")
        "default_popup": "popup.html", // HTML-файл для всплывающего окна
        "default_icon": {
          "48": "icons/icon48.png", // Иконки расширения
          "128": "icons/icon128.png"
        }
      },
      "icons": {
        "48": "icons/icon48.png",
        "128": "icons/icon128.png"
      },
      "content_scripts": [ // Скрипты, внедряемые на веб-страницы
        {
          "matches": ["*://*.youtube.com/*"], // На каких страницах запускать
          "js": ["content.js"] // Сам скрипт
        }
      ]
    }
    ```
*   `popup.html`: HTML-файл для всплывающего окна, которое появляется при клике на иконку расширения. Обычно содержит элементы интерфейса (кнопки, выпадающие списки).
*   `popup.js`: JavaScript-файл, связанный с `popup.html`. Обрабатывает события в попапе, взаимодействует с фоновым скриптом или скриптом контента для выполнения действий (например, получения данных или инициирования скачивания).
*   `content.js`: JavaScript-файл, который внедряется непосредственно на страницы YouTube. Он может получать информацию со страницы, взаимодействовать с DOM и отправлять сообщения расширению.
*   Файлы в папке `js/`: Содержат вспомогательные функции или классы, используемые другими скриптами расширения. Например, `js/downloader.js` содержит логику для формирования файла и вызова браузерного API для скачивания.

## Примеры использования

Предполагаемый сценарий использования расширения:

1.  Откройте любое видео на YouTube в вашем браузере.
2.  Нажмите на иконку расширения в панели инструментов браузера.
3.  В появившемся окне, возможно, будет выбор доступных языков субтитров или кнопка для скачивания. (Примечание: Текущая реализация может требовать доработки пользовательского интерфейса).
4.  Нажмите кнопку для скачивания субтитров.
5.  Браузер должен начать загрузку файла субтитров (обычно в формате `.txt`).

Пример кода из `js/downloader.js`, показывающий вызов API загрузки:

```
try {
  // Вызов API браузера для загрузки файла
  await browser.downloads.download(
  { // В Chrome используется chrome.downloads.download
    url: url, // URL Blob с содержимым файла
    filename: filename, // Имя файла для сохранения
    saveAs: false, // Не спрашивать пользователя куда сохранить
  });
} catch (error) {
}
```
(Примечание: В этом примере используется `browser.downloads.download` для совместимости с Firefox; в версии для Chrome используется `chrome.downloads.download`).

## Конфигурация

Основная конфигурация расширения задается в файле `manifest.json`. Важные параметры включают:

*   `manifest_version`: Указывает версию формата манифеста. Chrome 10+ использует `3`, Firefox и старые версии Chrome используют `2`. В этом проекте папка Chrome использует `3`, папка Firefox использует `2`.
*   `name`, `version`, `description`: Общая информация о расширении.
*   `permissions`: Массив строк, указывающий, к каким API или данным браузера расширение запрашивает доступ. В данном проекте используются `activeTab` (для взаимодействия с текущей вкладкой) и `downloads` (для скачивания файлов).
*   `action` (Chrome) / `browser_action` (Firefox): Определяет свойства кнопки расширения (иконка, всплывающее окно).
    *   `default_popup`: Путь к HTML-файлу всплывающего окна (по умолчанию `popup.html`).
    *   `default_icon`: Пути к файлам иконок разного размера.
*   `icons`: Пути к иконкам расширения, используемым в разных частях интерфейса браузера.
*   `content_scripts`: Определяет скрипты, которые должны быть внедрены в определенные веб-страницы.
    *   `matches`: Массив URL-шаблонов, определяющих страницы, на которых будет работать скрипт (например, `*://*.youtube.com/*` для всех страниц YouTube).
    *   `js`: Массив путей к JavaScript-файлам скриптов контента (по умолчанию `content.js`).

Параметры по умолчанию для путей к файлам (`popup.html`, `content.js`, иконки) указаны прямо в `manifest.json`.

```json
  "action": {
    "default_popup": "popup.html",
    "default_icon": {
      "48": "icons/icon48.png",
      "128": "icons/icon128.png"
    }
  },
```


