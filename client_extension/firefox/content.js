// Helper to set button final state
function setButtonFinalState(button, success) {
  button.disabled = true; // Always disable after operation
  //button.disabled = false;
  button.dispatchEvent(new Event("disabled-changed")); // Update visual disabled state
  if (success) {
    button.style.backgroundColor = "rgba(50, 200, 150, 0.3)"; // Slightly greenish with transparency
  } else {
    button.style.backgroundColor = "rgba(200, 150, 50, 0.3)"; // Slightly reddish with transparency
  }
}

// Функция для создания безопасного имени файла (во встроенной кнопке)
function createSafeFileName(title, date) {
  console.log("content.js loaded and running.");
  // Регулярное выражение для удаления недопустимых символов
  const sanitized = title
    .trim()
    .replace(/[\/:\"*?<>|]+/g, "_") // Удаляем недопустимые символы
    .replace(/\s+/g, " ") // Заменяем пробелы
    .replace(/_+/g, "_") // Заменяем множественные подчеркивания
    .replace(/^_+|_+$/g, ""); // Удаляем начальные/конечные подчеркивания
  const suffix = date ? `_${date.split("T")[0].replace(/-/g, "")}` : "";
  return `[Local] ${sanitized}${suffix} [DownSub-Alt]`;
}

// Функция для создания кнопки CC в стиле YouTube
function createCCButton() {
  const button = document.createElement("button");
  button.className =
    "yt-spec-button-shape-next yt-spec-button-shape-next--tonal yt-spec-button-shape-next--mono yt-spec-button-shape-next--size-m yt-spec-button-shape-next--icon-leading yt-spec-button-shape-next--enable-backdrop-filter-experiment";
  button.title = "Скачать субтитры";
  button.style.marginRight = "8px";

  // Стили для отключенного состояния
  button.addEventListener("disabled-changed", () => {
    if (button.disabled) {
      button.style.opacity = "0.5";
      button.style.cursor = "not-allowed";
    } else {
      button.style.opacity = "1";
      button.style.cursor = "pointer";
    }
  });

  // Создаем текстовое содержимое кнопки
  const textContent = document.createElement("div");
  textContent.className = "yt-spec-button-shape-next__button-text-content";
  textContent.textContent = "CC";

  button.appendChild(textContent);

  // Добавляем эффект при нажатии
  const touchFeedback = document.createElement("yt-touch-feedback-shape");
  touchFeedback.style.borderRadius = "inherit";
  const touchDiv = document.createElement("div");
  touchDiv.className =
    "yt-spec-touch-feedback-shape yt-spec-touch-feedback-shape--touch-response";
  touchDiv.setAttribute("aria-hidden", "true");

  const strokeDiv = document.createElement("div");
  strokeDiv.className = "yt-spec-touch-feedback-shape__stroke";
  const fillDiv = document.createElement("div");
  fillDiv.className = "yt-spec-touch-feedback-shape__fill";

  touchDiv.appendChild(strokeDiv);
  touchDiv.appendChild(fillDiv);
  touchFeedback.appendChild(touchDiv);
  button.appendChild(touchFeedback);

  // Добавляем обработчик события с делегированием
  button.onclick = function (event) {
    event.preventDefault();
    event.stopPropagation();
    handleCCButtonClick(event);
  };

  return button;
}

// Функция для вставки кнопки CC в интерфейс
function insertCCButton() {
  const topLevelButtons = document.querySelector("#top-level-buttons-computed");
  console.log("insertCCButton called. topLevelButtons:", topLevelButtons);
  if (topLevelButtons) {
    const ccButton = createCCButton();
    topLevelButtons.insertBefore(ccButton, topLevelButtons.firstChild);
  }
}

// Наблюдатель за изменениями в DOM для добавления кнопки при загрузке страницы
const observer = new MutationObserver((mutations) => {
  console.log("MutationObserver triggered.");
  for (const mutation of mutations) {
    if (mutation.addedNodes.length) {
      const topLevelButtons = document.querySelector(
        "#top-level-buttons-computed"
      );
      console.log("MutationObserver: topLevelButtons:", topLevelButtons);
      if (
        topLevelButtons &&
        !topLevelButtons.querySelector('button[title="Скачать субтитры"]')
      ) {
        console.log("MutationObserver: Inserting CC button.");
        insertCCButton();
        break;
      }
    }
  }
});

// Запускаем наблюдатель
observer.observe(document.body, {
  childList: true,
  subtree: true,
});

console.log("MutationObserver started.");

// Расширенная версия функции для обработки нажатия на кнопку CC
function handleCCButtonClick(event) {
  // Проверяем, что событие существует
  if (!event) return;

  console.log("CC кнопка была нажата!");

  // Получаем кнопку и URL текущего видео
  const button = event.target.closest("button");
  const videoUrl = document.location.href;

  // Создаем экземпляр API сервиса для субтитров
  const wsService = new SubtitlesAPI();

  // Отключаем кнопку перед запросом
  button.disabled = true;
  button.dispatchEvent(new Event("disabled-changed"));

  // Обработка статуса загрузки
  wsService.onStatusUpdate = (status) => {
    if (status === "processing") {
      console.log("Загрузка субтитров...");
      button.disabled = true;
      button.dispatchEvent(new Event("disabled-changed"));
    }
  };

  // Обработка ошибок
  wsService.onError = (error) => {
    console.error("Ошибка:", error);
    setButtonFinalState(button, false); // Call helper for error
  };

  // Обработка успешного получения субтитров
  wsService.onComplete = async (data) => {
    let downloadAttempted = false; // Flag to track if download was attempted
    let downloadSuccessful = false; // Flag to track if download was successful

    try {
      console.log("wsService.onComplete", data);

      // Функция для скачивания файла через программный клик по ссылке
      const downloadFile = (content, filename) => {
        const blob = new Blob([content], { type: "text/plain;charset=utf-8" });
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = filename;
        link.style.display = "none";
        document.body.appendChild(link);

        link.click();

        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      };

      // Загружаем русские субтитры, если оригинальный язык русский
      if (data.original_language === "ru" && data.ru_subtitles) {
        try {
          downloadAttempted = true;
          const filename = `${createSafeFileName(
            data.title,
            data.publish_date
          )}.txt`;
          downloadFile(data.ru_subtitles, filename);
          console.log(
            "Content: русские субтитры успешно отправлены на скачивание"
          );
          downloadSuccessful = true;
        } catch (error) {
          console.error(
            "Content: ошибка при скачивании русских субтитров",
            error
          );
          throw error;
        }
      }
      // Загружаем английские субтитры для всех остальных языков
      else if (data.en_subtitles) {
        try {
          downloadAttempted = true;
          const filename = `${createSafeFileName(
            data.title,
            data.publish_date
          )}.txt`;
          downloadFile(data.en_subtitles, filename);
          console.log("Content: english subtitles sent for download");
          downloadSuccessful = true;
        } catch (error) {
          console.error("Content: error downloading english subtitles", error);
          throw error;
        }
      }
      // Загружаем русские субтитры если оригинальный язык не русский и если en_subtitles пустые
      else if (data.ru_subtitles) {
        try {
          // Content кнопка загрузка английских субтитров
          downloadAttempted = true;
          const filename = `${createSafeFileName(
            data.title,
            data.publish_date
          )}.txt`;
          downloadFile(data.ru_subtitles, filename);
          console.log("Content: reset to russian subtitles sent for download");
          downloadSuccessful = true;
        } catch (error) {
          console.error("Content: error downloading russian subtitles", error);
          throw error;
        }
      }
      // Субтитры не найдены
      else {
        const error = new Error("Субтитры не найдены для этого видео");
        console.error("Content:", error);
        throw error;
      }
    } catch (error) {
      console.error("Не удалось сохранить файл субтитров", error);
    } finally {
      setButtonFinalState(button, downloadAttempted && downloadSuccessful);
      wsService.close();
    }
  };

  // Запрашиваем субтитры
  wsService.requestSubtitles(videoUrl).catch((error) => {
    console.error("Ошибка подключения к серверу", error);
    setButtonFinalState(button, false);
  });
}
