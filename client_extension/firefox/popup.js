document.addEventListener("DOMContentLoaded", () => {
  const downloadBtn = document.getElementById("downloadBtn");
  const statusDiv = document.getElementById("status");
  const languageInfoDiv = document.getElementById("language-info");

  // Create API service instance for subtitles
  const wsService = new SubtitlesAPI();

  // Update UI status
  const updateStatus = (message, type = "info") => {
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
  };

  // Handle WebSocket events
  wsService.onStatusUpdate = (status) => {
    if (status === "processing") {
      updateStatus("Downloading subtitles...", "info");
      downloadBtn.disabled = true;
    }
  };

  wsService.onError = (error) => {
    updateStatus(error, "error");
    downloadBtn.disabled = false;
  };

  wsService.onComplete = async (data) => {
    try {
      console.log("wsService.onComplete", data);

      // Display original language if available
      if (data.original_language) {
        const languageName = getLanguageName(data.original_language);
        languageInfoDiv.textContent = `Original language: ${languageName} (${data.original_language})`;
        languageInfoDiv.className = "language-info visible";
      } else {
        languageInfoDiv.className = "language-info";
      }

      // Download Russian subtitles if original language is Russian
      if (data.original_language === "ru" && data.ru_subtitles) {
        await SubtitlesDownloader.downloadSubtitles(
          data.title,
          data.ru_subtitles,
          data.publish_date,
          "ru"
        );
        updateStatus("Русские субтитры успешно сохранены!", "success");
      }
      // Download English subtitles for all other languages
      else if (data.en_subtitles) {
        await SubtitlesDownloader.downloadSubtitles(
          data.title,
          data.en_subtitles,
          data.publish_date,
          "en"
        );
        updateStatus("English subtitles successfully saved!", "success");
      } else if (data.ru_subtitles) {
        await SubtitlesDownloader.downloadSubtitles(
          data.title,
          data.ru_subtitles,
          data.publish_date,
          "en"
        );
        updateStatus("Reset to russian subtitles successfully!", "success");
      }
      // No appropriate subtitles found
      else {
        if (data.original_language === "ru") {
          updateStatus("Русские субтитры не найдены для этого видео", "error");
        } else {
          updateStatus("English subtitles not found for this video", "error");
        }
      }
    } catch (error) {
      updateStatus("Failed to save subtitles file", "error");
    }
    downloadBtn.disabled = false;
    wsService.close();
  };

  // Helper function to get language name from ISO code
  function getLanguageName(langCode) {
    const languages = {
      en: "English",
      ru: "Russian",
      fr: "French",
      de: "German",
      es: "Spanish",
      it: "Italian",
      ja: "Japanese",
      ko: "Korean",
      zh: "Chinese",
      ar: "Arabic",
      hi: "Hindi",
      pt: "Portuguese",
      nl: "Dutch",
      sv: "Swedish",
      fi: "Finnish",
      no: "Norwegian",
      da: "Danish",
      pl: "Polish",
      tr: "Turkish",
      uk: "Ukrainian",
      cs: "Czech",
      hu: "Hungarian",
      el: "Greek",
      he: "Hebrew",
      th: "Thai",
      vi: "Vietnamese",
    };

    return languages[langCode] || langCode;
  }

  // Check if we're on a YouTube video page
  chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
    const tab = tabs[0];
    const url = new URL(tab.url);

    if (url.hostname === "www.youtube.com" && url.pathname === "/watch") {
      downloadBtn.disabled = false;
      updateStatus("Ready to download subtitles", "info");
    } else {
      downloadBtn.disabled = true;
      updateStatus("Please open a YouTube video page", "error");
    }
  });

  // Handle download button click
  downloadBtn.addEventListener("click", async () => {
    try {
      chrome.tabs.query({ active: true, currentWindow: true }, async (tabs) => {
        const tab = tabs[0];
        downloadBtn.disabled = true;
        updateStatus("Connecting to server...", "info");

        try {
          await wsService.requestSubtitles(tab.url);
        } catch (error) {
          updateStatus("Failed to connect to server", "error");
          downloadBtn.disabled = false;
        }
      });
    } catch (error) {
      updateStatus("An error occurred", "error");
      downloadBtn.disabled = false;
    }
  });
});
