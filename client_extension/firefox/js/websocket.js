class SubtitlesAPI {
  constructor(baseUrl = "http://localhost:8001") {
    this.baseUrl = baseUrl;
    this.onStatusUpdate = null;
    this.onError = null;
    this.onComplete = null;
    this.currentTaskId = null;
    this.pollingInterval = null;
  }

  async requestSubtitles(youtubeUrl) {
    try {
      const response = await fetch(`${this.baseUrl}/subtitles`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ url: youtubeUrl }),
      });
      const data = await response.json();
      this.currentTaskId = data.task_id;
      this.sseUrl = data.sse_url;
      if (data.status === "completed") {
        this.fetchResult();
      } else {
        this.onStatusUpdate && this.onStatusUpdate(data.status);
        this.startSSE();
      }
    } catch (error) {
      this.onError && this.onError(error.message);
    }
  }

  startSSE() {
    if (this.eventSource) return;
    this.isCompleted = false;
    this.sseRetryCount = 0;
    this.connectSSE();
  }

  connectSSE() {
    this.eventSource = new EventSource(this.sseUrl);
    console.log("SSE Connecting to:", this.sseUrl); // Debug log for connection attempt
    this.eventSource.addEventListener("update", (event) => {
      const data = JSON.parse(event.data);
      console.log("SSE Update:", data); // Debug log to track received events
      this.onStatusUpdate && this.onStatusUpdate(data.status);
      if (data.status === "completed") {
        this.isCompleted = true;
        this.eventSource.close();
        this.eventSource = null;
        this.fetchResult();
      } else if (data.status === "failed" || data.error) {
        this.eventSource.close();
        this.eventSource = null;
        this.onError && this.onError(data.error || "Task failed");
      }
    });
    this.eventSource.onerror = (error) => {
      console.error("SSE Error:", error); // Debug log for errors
      if (this.eventSource) {
        this.eventSource.close();
        this.eventSource = null;
      }
      // Reconnect only if the status is not completed and task ID exists, with a retry limit
      if (!this.currentTaskId) {
        this.onError && this.onError("SSE connection error - no task ID");
      } else if (!this.isCompleted) {
        this.sseRetryCount++;
        if (this.sseRetryCount < 1) {
          console.log(
            "SSE Reconnecting in 5 seconds... Attempt " + this.sseRetryCount
          );
          setTimeout(() => {
            if (this.currentTaskId && !this.eventSource && !this.isCompleted) {
              console.log("SSE Reconnection attempt");
              this.connectSSE();
            }
          }, 5000); // Reconnect after 5 seconds
        } else {
          console.log(
            "SSE connection failed after " +
              this.sseRetryCount +
              " attempts. Falling back to polling."
          );
          this.startPolling();
        }
      } else {
        console.log(
          "SSE Connection closed after completion, no reconnection needed."
        );
      }
    };
  }

  startPolling() {
    if (this.pollingInterval) return;
    console.log("Starting polling for status updates...");
    this.pollingInterval = setInterval(async () => {
      if (!this.currentTaskId) return;
      try {
        const response = await fetch(
          `${this.baseUrl}/tasks/${this.currentTaskId}/status`
        );
        const data = await response.json();
        this.onStatusUpdate && this.onStatusUpdate(data.status);
        if (data.status === "completed") {
          clearInterval(this.pollingInterval);
          this.pollingInterval = null;
          this.fetchResult();
        } else if (data.error) {
          clearInterval(this.pollingInterval);
          this.pollingInterval = null;
          this.onError && this.onError(data.error);
        }
      } catch (error) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
        this.onError && this.onError(error.message);
      }
    }, 5000); // Poll every 5 seconds
  }

  async fetchResult() {
    try {
      const response = await fetch(
        `${this.baseUrl}/tasks/${this.currentTaskId}/result`
      );
      const data = await response.json();
      if (data.error) {
        this.onError && this.onError(data.error);
      } else {
        this.onComplete &&
          this.onComplete({
            title: data.result.title,
            original_language: data.result.original_language,
            publish_date: data.result.upload_date,
            ru_subtitles: data.result.ru_subtitles,
            en_subtitles: data.result.en_subtitles,
          });
      }
    } catch (error) {
      this.onError && this.onError(error.message);
    }
  }

  // Method already defined above

  close() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.currentTaskId = null;
    this.sseUrl = null;
    this.isCompleted = false;
  }
}
