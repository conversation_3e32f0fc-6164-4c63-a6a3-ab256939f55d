version: '3.8'
services:
  redis:
    image: redis:7
    ports:
      - "6379:6379"

  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: app_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data/

  minio:
    image: minio/minio
    ports:
      - "9020:9000"
      - "9021:9001"
    volumes:
      - minio_storage:/data
    environment:
      MINIO_ROOT_USER: user
      MINIO_ROOT_PASSWORD: password
    command: server --console-address ":9001" /data

volumes:
  postgres_data:
  minio_storage: {}